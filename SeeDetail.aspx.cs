﻿using System;
using System.Data;
using System.Web;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Text;
using System.Linq;
public partial class SeeTask : System.Web.UI.Page
{
    string pKey = "id";
    readonly string ConnStr = InputParams.connectionStr;
    public int LastRecordCount { get; private set; } = 0;//记录数
    public EnumField[][] EnumField()
    {
        EnumField[] enum0 = { new EnumField(0, "跟进中"), new EnumField(1, "已确认"), new EnumField(2, "已合作"), new EnumField(3, "未合作"), new EnumField(4, "待联系") };
        EnumField[] enum1 = { new EnumField(0, " "), new EnumField(1, "★"), new EnumField(2, "★★"), new EnumField(3, " ★★★"), new EnumField(4, "★★★★"), new EnumField(5, "★★★★★") };
        EnumField[] enum2 = { new EnumField(2, "盒饭"), new EnumField(1, "桶饭"), new EnumField(2, "食堂"), new EnumField(3, "个人解决") };

        EnumField[][] efTs = { enum0, enum1, enum2 };
        return efTs;
    }

    public Table[] Tables()
    {
        string tblName1 = "FollowClient";

        Field fld0 = new Field(0, "firm_name", "公司名称", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld1 = new Field(1, "linkman", "联系人", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld2 = new Field(2, "telephone", "电话", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld3 = new Field(3, "email", "Email", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld4 = new Field(4, "userAddress", "地址", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld5 = new Field(11, "area", "城市", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld6 = new Field(12, "last_follow_time", "最后跟踪时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld7 = new Field(13, "client_state", "客户状态", 0, EnumFieldType.enumType, 0, 0, "", 0, "");
        Field fld8 = new Field(15, "Type", "客户意向指数", 0, EnumFieldType.enumType, 0, 1, "", 0, "");
        Field fld9 = new Field(16, "userAddress", "地址", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld10 = new Field(17, "next_follow_time", "预计下次回访时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld11 = new Field(18, "creat_time", "创建时间", 0, EnumFieldType.dateType, 0, 0, "", 0, "");
        Field fld12 = new Field(19, "investigate", "备注", 0, EnumFieldType.charType, 0, 0, "", 0, "");
        Field fld13 = new Field(20, "follow_number", "追加记录的次数", 0, EnumFieldType.numberType, 0, 0, "", 0, "");
        Field[] flds = { fld0, fld1, fld2, fld3, fld4, fld5, fld6, fld7, fld8, fld9, fld10, fld11, fld12, fld13 };
        Join jon1 = new Join();
        Table table1 = new Table(tblName1, jon1, flds);

        Table[] tables = { table1 };
        return tables; ;
    }
    //判断是否是手机端
    public bool isMobile()
    {
        var context = HttpContext.Current;
        if (context == null)
            return false;

        var userAgent = context.Request.UserAgent?.ToLower() ?? string.Empty;
        string[] mobileDevices = { "iphone", "android", "windows phone", "mobile" };
        for (int i = 0; i < mobileDevices.Length; i++)
        {
            if (userAgent.Contains(mobileDevices[i]))
                return true;
        }

        return false;
    }
    /// <summary>
    /// 追加记录图片表实体（映射 FollowRecordImages 表）
    /// </summary>
    public class FollowRecordImage
    {
        /// <summary>图片表主键</summary>
        public int Id { get; set; }
        /// <summary>关联追踪记录的外键（主表 FollowRecord 的 ID）</summary>
        public int RecordId { get; set; }
        /// <summary>图片二进制数据（BLOB 类型）</summary>
        public byte[] ImageData { get; set; }
        /// <summary>文件名</summary>
        public string FileName { get; set; }
        /// <summary>内容类型（如 image/png）</summary>
        public string ContentType { get; set; }
        /// <summary>上传时间</summary>
        public DateTime CreatedAt { get; set; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
		HttpCookie cookie = Request.Cookies["user"];
		if (cookie != null)//不用重新登录
			{
				Session["user_id"] = cookie.Values["user_id"].ToString();//用户的ID
				Session["isGroupManager"] = cookie.Values["isGroupManager"].ToString();
		}
		else
		{
			Response.Redirect("Login.aspx");
		}
        int TaskID = int.Parse(Request.QueryString["TaskID"].ToString());
        string strSql = GetSqlStr(TaskID);
        GenerateSeeMould(strSql);

        if (Request.QueryString["message"] != null)
        {
            ClientScript.RegisterClientScriptBlock(this.GetType(), "info", "<script language=\"javascript\" type=\"text/javascript\">alert(\"添加成功！\");window.location.href='See.aspx';</script>");

        }

        int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
        if ((iGroupManager == 1 || iGroupManager == 2) && !isMobile())
        {
            deskFooterDiv.Visible = true;
            mobileFooterDiv.Visible = false;
        }
        else if ((iGroupManager == 1 || iGroupManager == 2) && isMobile())
        {
            deskFooterDiv.Visible = false;
            mobileFooterDiv.Visible = true;
        }
        else if (isMobile())
        {
            deskFooterDiv.Visible = false;
            mobileFooterDiv.Visible = true;
        }
        else
        {
            deskFooterDiv.Visible = true;
            mobileFooterDiv.Visible = false;
        }

    }

    protected void hlkReturn_Click(object sender, EventArgs e)
    {
        Response.Write("<script language:javascript>javascript:window.close();</script>");

        //Response.Redirect("See.aspx?id=2");
    }

    protected void hlkModify_Click(object sender, EventArgs e)
    {
		int TaskID = int.Parse(Request.QueryString["TaskID"].ToString());

        int[] arrModify = new int[1];
        arrModify[0] = TaskID;
        Session["myId"] = TaskID;    //当前客户ID【跟踪表】
        Session["ckbsModify"] = arrModify;
        //Response.Write("<script language:javascript>window.location.href='Modify.aspx?recordId='" + TaskID + "</script>");

        //Page.ClientScript.RegisterStartupScript(this.GetType(), "", "<script>window.open('Modify.aspx?recordId=" + TaskID + "', '_blank')</script>");
        /*RegisterStartupScript("js", "<script>window.open('Modify.aspx?recordId=" + TaskID + "', '_blank')</script>");*/

        //ScriptManager.RegisterStartupScript(this, this.GetType(), "js", "window.open('Modify.aspx?recordId=" + TaskID + "', '_blank');", true);//本地可以用

		Response.Redirect("Modify.aspx?recordId=" + TaskID);
	}

    protected void hlkAddRecod_Click(object sender, EventArgs e)
    {
        int TaskID = int.Parse(Request.QueryString["TaskID"].ToString());
        //Response.Write("<script language:javascript>window.location.href='AddRecord.aspx?id='" + TaskID + "</script>");

        Response.Redirect("AddRecord.aspx?id=" + TaskID);
    }

    // 用户id昵称映射缓存
    private static Dictionary<int, string> userNicknameCache = new Dictionary<int, string>();
    /// <summary>
    /// 根据用户的ID而获得用户姓名
    /// </summary>
    /// <param name="task_id"></param>
    /// <returns></returns>
    public string UserID_to_Nickname(int user_id)
    {
        if (userNicknameCache.ContainsKey(user_id))
        {
            //Logger.Log($"命中缓存:{user_id} -> {userNicknameCache[user_id]}");
            return userNicknameCache[user_id];
        }

        SqlConnection conn = new SqlConnection(ConnStr);
        string name = "";

        if (conn.State.ToString() == "Closed")
        {
            conn.Open();
        }

        string strSql = "select nickname from Users where Users.user_id=" + user_id;
        SqlCommand comm = new SqlCommand(strSql, conn);
        SqlDataReader dr = comm.ExecuteReader();
        if (dr.Read())
        {
            name = dr["nickname"].ToString();
            userNicknameCache[user_id] = name;  // 缓存昵称
        }
        dr.Close();
        if (conn.State.ToString() == "Open")
        {
            conn.Close();
        }

        return name;

    }

    //public string GetRecord(int id)
    //{
    //    SqlConnection conn = new SqlConnection(ConnStr);

    //    string record = "";

    //    if (conn.State.ToString() == "Closed")
    //    {
    //        conn.Open();
    //    }

    //    string strSql = "select description,follow_time,follow_executor from FollowRecord where follow_client_id =" + id + " order by follow_time DESC";
    //    SqlCommand comm = new SqlCommand(strSql, conn);
    //    SqlDataReader dr = comm.ExecuteReader();


    //    if (isMobile())
    //    {
    //        record += "<div id ='records' class='section hidden'><h1>跟踪记录</h1>";
    //        // 移动端：卡片列表
    //        while (dr.Read())
    //        {
    //            string nickname = UserID_to_Nickname(int.Parse(dr["follow_executor"].ToString()));
    //            string time = dr["follow_time"].ToString();
    //            string desc = dr["description"].ToString();

    //            record += "<div style='border-radius: 16px; box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06); padding: 16px; background: white; margin-bottom: 16px;'>";
    //            record += "<div class='record' style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;'>";
    //            record += $"<span style='font-weight: bold;'>{nickname}</span>";
    //            record += $"<span style='font-size: 14px; color: #6b7280;'>{time}</span>";
    //            record += "</div>";
    //            record += $"<div class='record' style='color: #374151;'>{desc}</div>";
    //            record += "</div>";
    //        }
    //        record += "</div>";
    //    }
    //    else
    //    {
    //        // 桌面端：表格
    //        record += "<table border='1' style='text-align:center; width:100%; border-collapse: collapse;'>";
    //        record += "<tr style='background-color: #f0f0f0;'>";
    //        record += "<th style='width:450px; padding: 8px;'>内容</th>";
    //        record += "<th style='padding: 8px;'>跟踪时间</th>";
    //        record += "<th style='padding: 8px;'>跟踪者</th>";
    //        record += "</tr>";

    //        while (dr.Read())
    //        {
    //            record += "<tr>";
    //            record += "<td style='width:450px; padding: 8px; text-align:left;'>" + dr["description"].ToString() + "</td>";
    //            record += "<td style='padding: 8px;'>" + dr["follow_time"].ToString() + "</td>";
    //            record += "<td style='padding: 8px;'>" + UserID_to_Nickname(int.Parse(dr["follow_executor"].ToString())) + "</td>";
    //            record += "</tr>";
    //        }
    //        record += "</table>";
    //    }

    //    dr.Close();
    //    if (conn.State.ToString() == "Open")
    //    {
    //        conn.Close();
    //    }

    //    return record;
    //}

    ///// <summary>
    ///// 生成查看模板 2025-6-5修改
    ///// </summary>
    //protected void GenerateSeeMould(string strSql)
    //{
    //    int flag = 0;
    //    placeHolder1.Controls.Clear();

    //    SqlConnection conn = new SqlConnection(ConnStr);
    //    conn.Open();
    //    SqlDataAdapter da = new SqlDataAdapter(strSql, conn);
    //    DataSet ds = new DataSet();
    //    da.Fill(ds);
    //    DataTable dt = ds.Tables[0];
    //    DataRow dr = dt.Rows[0];

    //    EnumField[][] efTs = EnumField();
    //    Table[] tables = Tables();

    //    Literal ltlBrs0 = new Literal();
    //    if (isMobile()) {
    //        ltlBrs0.Text = "<div id='info' class='section'><h1>客户信息</h1>";
    //        placeHolder1.Controls.Add(ltlBrs0);
    //    }


    //    #region forTable
    //    int colIndex = -1;
    //    for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
    //    {
    //        #region forField
    //        for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
    //        {
    //            colIndex++;
    //            Literal ltlBrs = new Literal();
    //            ltlBrs.Text = "<br/>";
    //            placeHolder1.Controls.Add(ltlBrs);


    //            Label lbl = new Label();
    //            lbl.Text = "<div class='value'><span class='label'>"+ tables[tableIndex].fields[fieldIndex].fieldShowName + "："+ "</span>";
    //            lbl.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
    //            placeHolder1.Controls.Add(lbl);

    //            #region switch
    //            string strFldName = tables[tableIndex].fields[fieldIndex].fieldName;

    //            switch (tables[tableIndex].fields[fieldIndex].fieldType)
    //            {

    //                case EnumFieldType.numberType:
    //                    if (tables[tableIndex].fields[fieldIndex].fieldName == "PlanSpendTime")
    //                    {
    //                        int spendTime = int.Parse(dr[colIndex].ToString());
    //                        //以分钟为单位
    //                        //int spendYear = spendTime / (24 * 60 * 365);
    //                        //int spendMonth = (spendTime % (24 * 60 * 365)) / (24 * 60 * 30);
    //                        //int spendDay = (((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) / (24 * 60));
    //                        //int spendHour = (((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) % (24 * 60)) / 60;
    //                        //int spendMinute = ((((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) % (24 * 60))) % 60;

    //                        int spendHour = spendTime / 60;
    //                        int spendMinute = spendTime % 60;

    //                        string strSpendTime = "";
    //                        //if (spendYear != 0)
    //                        //{
    //                        //    strSpendTime += spendYear + "年";
    //                        //}
    //                        //if (spendMonth != 0)
    //                        //{
    //                        //    strSpendTime += spendMonth + "个月";

    //                        //}
    //                        //if (spendDay != 0)
    //                        //{
    //                        //    strSpendTime += spendDay + "天";

    //                        //}
    //                        if (spendHour != 0)
    //                        {
    //                            strSpendTime += spendHour + "小时";

    //                        }
    //                        if (spendMinute != 0)
    //                        {
    //                            strSpendTime += spendMinute + "分钟";
    //                        }

    //                        Literal ltlN = new Literal();
    //                        ltlN.Text = strSpendTime;
    //                        placeHolder1.Controls.Add(ltlN);

    //                    }
    //                    else
    //                    {
    //                        Literal ltlN = new Literal();
    //                        if (isMobile())
    //                        {
    //                            ltlN.Text = dr[colIndex].ToString()+"</div>";
    //                        }
    //                        else {
    //                            ltlN.Text = dr[colIndex].ToString();
    //                        }

    //                        placeHolder1.Controls.Add(ltlN);
    //                    }
    //                    continue;

    //                case EnumFieldType.boolType:

    //                    string text = "";
    //                    if (flag != 2)
    //                    {
    //                        if (int.Parse(dr[colIndex].ToString()) == 1)
    //                            text = "是";
    //                        else
    //                            text = "否";
    //                        flag++;

    //                    }
    //                    else
    //                    {
    //                        if (int.Parse(dr[colIndex].ToString()) == 1)
    //                            text = "男";
    //                        else
    //                            text = "女";
    //                        flag = 1;
    //                    }

    //                    Literal ltlB = new Literal();
    //                    ltlB.Text = text;
    //                    placeHolder1.Controls.Add(ltlB);
    //                    continue;

    //                //case EnumFieldType.picType:

    //                //    continue;

    //                case EnumFieldType.enumType:
    //                    int enumTag = tables[tableIndex].fields[fieldIndex].enumTag;
    //                    int enumItem = int.Parse(dr[colIndex].ToString());
    //                    Literal ltlE = new Literal();
    //                    bool isTrue = false;
    //                    if (tables[tableIndex].fields[fieldIndex].fieldName == "area" && isMobile()) 
    //                    {
    //                        ltlE.Text = (new Common()).GetTown(enumItem) + "</div>";
    //                        isTrue = true;
    //                    }  
    //                    else if(tables[tableIndex].fields[fieldIndex].fieldName == "area")
    //                    {
    //                        ltlE.Text = (new Common()).GetTown(enumItem);
    //                        isTrue = true;
    //                    }

    //                    if (isTrue != true && isMobile())
    //                    {
    //                        ltlE.Text = efTs[enumTag][enumItem].itemDetail + "</div>";
    //                    }
    //                    else if(isTrue != true)
    //                    {
    //                        ltlE.Text = efTs[enumTag][enumItem].itemDetail ;
    //                    }  

    //                    placeHolder1.Controls.Add(ltlE);
    //                    continue;
    //                case EnumFieldType.longcharType:

    //                    if (dr[colIndex].ToString() != "")
    //                    {
    //                        Literal ltlBr1 = new Literal();
    //                        ltlBr1.Text = "<br/>";
    //                        placeHolder1.Controls.Add(ltlBr1);


    //                        Label lblD1 = new Label();
    //                        if (isMobile())
    //                        {
    //                            lblD1.Text = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + dr[colIndex].ToString()+"</div>";
    //                        }
    //                        else {
    //                            lblD1.Text = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + dr[colIndex].ToString();
    //                        }

    //                        lblD1.ForeColor = System.Drawing.ColorTranslator.FromHtml("#0000FF");
    //                        //lblD1.BackColor = System.Drawing.ColorTranslator.FromHtml("#F6FFFF");
    //                        //lblD1.Height = 50;
    //                        lblD1.Width = 700;
    //                        placeHolder1.Controls.Add(lblD1);

    //                        ////生成换行符
    //                        //Literal ltlBr2 = new Literal();
    //                        //ltlBr2.Text = "<br/>";
    //                        //placeHolder1.Controls.Add(ltlBr2);
    //                    }
    //                    continue;

    //                //case EnumFieldType.dateType:
    //                //    continue;
    //                //case EnumFieldType.charType:
    //                //    continue;

    //                default:
    //                    if ((dr[colIndex] != null) && (dr[colIndex].ToString() != ""))
    //                    {
    //                        Literal ltlT = new Literal();
    //                        if (isMobile())
    //                        {
    //                            ltlT.Text = dr[colIndex].ToString() + "</div>";
    //                        }
    //                        else {
    //                            ltlT.Text = dr[colIndex].ToString();
    //                        }
    //                        placeHolder1.Controls.Add(ltlT);
    //                    }
    //                    continue;

    //            }

    //            #endregion switch
    //        }
    //        #endregion forField
    //    }
    //    #endregion  forTable
    //    Literal ltlBrs2 = new Literal();
    //    ltlBrs2.Text = "</div>";
    //    placeHolder1.Controls.Add(ltlBrs2);
    //    //添加追加记录

    //    Literal ltlRecord = new Literal();

    //    ltlRecord.Text = "<br> <p style='color:#871F78;'>追加记录：</p><br>" + GetRecord(int.Parse(Request.QueryString["TaskID"].ToString()));

    //    placeHolder1.Controls.Add(ltlRecord);

    //}



    /// <summary>
    /// 生成“每行3张”的图片 HTML 结构
    /// </summary>
    /// <param name="images">图片列表</param>
    /// <returns>图片网格的 HTML 字符串</returns>
    //private string GenerateImageGridHtml(List<FollowRecordImage> images)
    //{
    //    if (images == null || images.Count == 0)
    //        return "<p>无关联图片</p>";

    //    string html = "<div class='image-grid'>";
    //    int imageCount = 0; // 计数，控制每行3张

    //    foreach (var img in images)
    //    {
    //        // 每行满3张则换行
    //        if (imageCount > 0 && imageCount % 3 == 0)
    //        {
    //            html += "<br/>";
    //        }

    //        // 生成 Base64 格式的图片标签
    //        string base64Src = ConvertToBase64(img.ImageData, img.ContentType);
    //        html += $@"
    //        <img 
    //            src='{base64Src}' 
    //            alt='{img.FileName}' 
    //            style='max-width:100px; max-height:100px; margin:4px;' 
    //        />
    //    ";
    //        imageCount++;
    //    }

    //    html += "</div>";
    //    return html;
    //}

    //private string GenerateImageGridHtml(List<FollowRecordImage> images)
    //{
    //	if (images == null || images.Count == 0)
    //		return "<p>无关联图片</p>";
    //	// 【步骤1：分组】每3张图片为一组（利用索引分组）
    //	var imageGroups = images
    //		.Select((img, index) => new { Img = img, GroupIndex = index / 3 }) // 每3个同组（index/3取整）
    //		.GroupBy(x => x.GroupIndex)
    //		.ToList();

    //	string html = "<div class='image-grid-wrapper'>"; // 外层容器

    //	// 【步骤2：遍历分组，生成HTML】
    //	for (int i = 0; i < imageGroups.Count; i++)
    //	{
    //		var group = imageGroups[i];
    //		var groupImages = group.Select(x => x.Img).ToList();

    //		// 控制显示/隐藏：第1组显示，后续组隐藏（加 hidden 类）
    //		string groupClass = i == 0 ? "image-row" : "image-row hidden";
    //		html += $"<div class='{groupClass}'data-group='{i}'>";

    //		// 生成组内的图片标签（Base64 嵌入）
    //		foreach (var img in groupImages)
    //		{
    //			string base64Src = ConvertToBase64(img.ImageData, img.ContentType);
    //			html += $@"
    //               <img 
    //                   src='{base64Src}' 
    //                   alt='{img.FileName}' 
    //                   loading='lazy'
    //                   style='max-width:100px; max-height:100px; margin:4px;cursor:zoom-in;'
    //                   onclick=""event.stopPropagation(); event.preventDefault();openLightbox('{base64Src}')""
    //               />
    //           ";
    //		}

    //		html += "</div>";

    //		// 【步骤3：添加展开按钮】仅第1组且存在更多组时显示
    //		if (i == 0 && imageGroups.Count > 1)
    //		{
    //			int hiddenGroupCount = imageGroups.Count - 1;
    //			html += $@"
    //               <button class='expand-btn' onclick="" event.preventDefault(); toggleImageGroups(this,{hiddenGroupCount})"" >
    //                   >> 展开更多（共 {hiddenGroupCount} 组）
    //               </button>
    //           ";
    //		}
    //	}
    //	// 添加灯箱功能
    //	html += @"
    //       <div id='lightbox' class='lightbox' style='
    //           position: fixed; 
    //           top: 0; left: 0; 
    //           width: 100%; height: 100%; 
    //           background: rgba(0,0,0,0.8); 
    //           display: none; 
    //           z-index: 1000;
    //           justify-content: center;
    //           align-items: center;
    //       '>
    //           <img id='lightbox-img' style='max-width: 90%; max-height: 90%;'>
    //           <button style='
    //               position: absolute; 
    //               top: 20px; 
    //               right: 20px; 
    //               font-size: 30px; 
    //               color: white; 
    //               background: transparent; 
    //               border: none;
    //               cursor: pointer;
    //           ' onclick='closeLightbox()'>×</button>
    //       </div>";

    //	// 【内联CSS】控制布局与隐藏逻辑
    //	html += @"
    //       <style>
    //           .image-grid-wrapper { margin-top: 8px; }
    //           .image-row { display: flex; flex-wrap: wrap; gap: 4px; margin-bottom: 8px; }
    //           .hidden { display: none; }
    //           .expand-btn { 
    //               cursor: pointer; 
    //               margin-top: 4px;
    //               background: #f0f2f5;
    //               border: 1px solid #d9d9d9;
    //               padding: 4px 8px;
    //               border-radius: 4px;
    //           }
    //           .expand-btn:hover {
    //               background: #e6f7ff;
    //               border-color: #40a9ff;
    //           }
    //       </style>
    //   ";

    //	// 【内联JS】点击按钮显示所有隐藏组，隐藏自身（添加灯箱功能）
    //	html += @"
    //       <script>
    //           function toggleImageGroups(btn, hiddenGroupCount) {
    //               event.stopPropagation();
    //               // 👉 步骤1：精准选择“后续组”（data-group ≥1 的 .image-row）
    //               var hiddenRows = Array.from(
    //                   btn.parentNode.querySelectorAll('.image-row[data-group]:not([data-group=""0""])'
    //               ));

    //               // 👉 步骤2：判断当前状态（后续组是否可见）
    //               var isExpanded = false;
    //               if (hiddenRows.length > 0) {
    //                   // 通过计算样式判断第一个后续组是否显示（display === 'flex' 表示可见）
    //                   isExpanded = window.getComputedStyle(hiddenRows[0]).display === 'flex';
    //               }

    //               // 👉 步骤3：切换状态
    //               if (!isExpanded) {
    //                   // 展开逻辑：移除所有后续组的 .hidden 类（显示内容）
    //                   hiddenRows.forEach(row => row.classList.remove('hidden'));
    //                   btn.innerText = `<< 收回（共 ${hiddenGroupCount} 组）`;
    //               } else {
    //                   // 收回逻辑：给所有后续组添加 .hidden 类（隐藏内容）
    //                   hiddenRows.forEach(row => row.classList.add('hidden'));
    //                   btn.innerText = `>> 展开更多（共 ${hiddenGroupCount} 组）`;
    //               }
    //           }

    //           function openLightbox(src) {
    //               event.stopPropagation();
    //               document.getElementById('lightbox-img').src = src;
    //               document.getElementById('lightbox').style.display = 'flex';
    //           }

    //           function closeLightbox() {
    //               event.preventDefault();
    //               document.getElementById('lightbox').style.display = 'none';
    //           }

    //           // 点击任意位置关闭灯箱
    //           document.addEventListener('click', function(e) {
    //               if (e.target.classList.contains('lightbox')) {
    //                   closeLightbox();
    //               }
    //           });
    //       </script>
    //   ";

    //	html += "</div>";
    //	return html;
    //}
    //实现了左右按钮点击翻动每组图片
    //private string GenerateImageGridHtml(List<FollowRecordImage> images)
    //{
    //	if (images == null || images.Count == 0)
    //		return "<p>无关联图片</p>";

    //	// 创建唯一ID
    //	string lightboxId = "lb_" + Guid.NewGuid().ToString("N");
    //	string counterId = "cnt_" + Guid.NewGuid().ToString("N");
    //	string containerId = "ctn_" + Guid.NewGuid().ToString("N");

    //	// 分组（每3张一组）
    //	var imageGroups = images
    //		.Select((img, idx) => new { Img = img, Idx = idx })
    //		.GroupBy(x => x.Idx / 3)
    //		.ToList();

    //	StringBuilder html = new StringBuilder();
    //	html.Append("<div class='image-grid-wrapper'>");

    //	// 生成图片网格
    //	for (int i = 0; i < imageGroups.Count; i++)
    //	{
    //		var group = imageGroups[i];
    //		string groupClass = i == 0 ? "image-row" : "image-row hidden";

    //		html.Append($"<div class='{groupClass}' data-group='{i}'>");

    //		foreach (var item in group)
    //		{
    //			string base64Src = ConvertToBase64(item.Img.ImageData, item.Img.ContentType);
    //			html.Append($@"
    //               <div class='preview-item' onclick=""event.preventDefault(); openLightbox({item.Idx}, '{lightboxId}', '{counterId}', '{containerId}', {images.Count})"">
    //                   <img src='{base64Src}' alt='{item.Img.FileName}' loading='lazy' style='width:100px;height:100px;margin:4px;cursor:zoom-in;'>
    //               </div>
    //           ");
    //		}

    //		html.Append("</div>");

    //		// 添加展开按钮（如果有隐藏组）
    //		if (i == 0 && imageGroups.Count > 1)
    //		{
    //			int hiddenCount = imageGroups.Count - 1;
    //			html.Append($@"
    //               <button class='expand-btn' onclick=""event.preventDefault();toggleGroups(this, {hiddenCount})"">
    //                   >> 展开更多（共 {hiddenCount} 组）
    //               </button>
    //           ");
    //		}
    //	}

    //	// 灯箱结构
    //	html.Append($@"
    //       <div id='{lightboxId}' class='lightbox' style='
    //           position:fixed;top:0;left:0;width:100%;height:100%;
    //           background:rgba(0,0,0,0.9);display:none;z-index:1000;
    //           justify-content:center;align-items:center;'>

    //           <div style='position:relative;width:90%;max-width:1200px;height:90vh;'>
    //               <!-- 计数器 -->
    //               <div id='{counterId}' style='
    //                   position:absolute;top:20px;left:50%;transform:translateX(-50%);
    //                   color:white;font-size:1.2rem;background:rgba(0,0,0,0.5);
    //                   padding:8px 20px;border-radius:20px;z-index:10;'>
    //                   1/{images.Count}
    //               </div>

    //               <!-- 图片容器 -->
    //               <div id='{containerId}' style='
    //                   width:100%;height:100%;position:relative;overflow:hidden;'>
    //               </div>

    //               <!-- 导航按钮 -->
    //               <button onclick='event.preventDefault();changeImage(-1)' style='
    //                   position:absolute;top:50%;left:2px;transform:translateY(-50%);
    //                   width:40px;height:40px;background:rgba(0,0,0,0.3);border:none;
    //                   border-radius:50%;color:white;font-size:24px;cursor:pointer;z-index:10;'>
    //                   ❮
    //               </button>

    //               <button onclick='event.preventDefault(); changeImage(1)' style='
    //                   position:absolute;top:50%;right:2px;transform:translateY(-50%);
    //                   width:40px;height:40px;background:rgba(0,0,0,0.3);border:none;
    //                   border-radius:50%;color:white;font-size:24px;cursor:pointer;z-index:10;'>
    //                   ❯
    //               </button>

    //               <!-- 关闭按钮 -->
    //               <button onclick='event.preventDefault(); closeLightbox()' style='
    //                   position:absolute;top:20px;right:20px;font-size:30px;
    //                   color:white;background:transparent;border:none;cursor:pointer;z-index:11;'>
    //                   ×
    //               </button>
    //           </div>
    //       </div>
    //   ");

    //	// 内联CSS
    //	html.Append(@"
    //       <style>
    //           .image-grid-wrapper { margin-top:8px; }
    //           .image-row { display:flex; flex-wrap:wrap; gap:4px; margin-bottom:8px; }
    //           .hidden { display:none; }
    //           .preview-item { width:100px; height:100px; overflow:hidden; cursor:pointer; }
    //           .preview-item:hover { transform:scale(1.05); transition:transform 0.2s; }
    //           .preview-item img { width:100%; height:100%; object-fit:cover; }
    //           .expand-btn { 
    //               cursor:pointer; margin-top:4px; background:#f0f2f5;
    //               border:1px solid #d9d9d9; padding:4px 8px; border-radius:4px; 
    //           }
    //           .lightbox-slide { 
    //               position:absolute; width:100%; height:100%; 
    //               display:flex; justify-content:center; align-items:center; 
    //           }
    //           .lightbox-slide img { max-width:100%; max-height:100%; object-fit:contain; }
    //       </style>
    //   ");

    //	// 内联JS
    //	html.Append($@"
    //       <script>
    //           // 灯箱状态
    //           let currentIndex = 0;
    //           let totalImages = 0;
    //           let lightboxElem, counterElem, containerElem;

    //           // 打开灯箱
    //           function openLightbox(index, lightboxId, counterId, containerId, total) {{
    //               currentIndex = index;
    //               totalImages = total;
    //               lightboxElem = document.getElementById(lightboxId);
    //               counterElem = document.getElementById(counterId);
    //               containerElem = document.getElementById(containerId);
    //               lightboxElem.style.display = 'flex';
    //               updateLightboxImage();
    //           }}

    //           // 切换图片
    //           function changeImage(offset) {{
    //               currentIndex = (currentIndex + offset + totalImages) % totalImages;
    //               updateLightboxImage();
    //           }}

    //           // 更新灯箱图片
    //           function updateLightboxImage() {{
    //               if (!containerElem) return;

    //               // 获取预览图中的对应图片
    //               const previewImg = document.querySelectorAll('.preview-item')[currentIndex].querySelector('img');

    //               containerElem.innerHTML = `
    //                   <div class='lightbox-slide'>
    //                       <img src='${{previewImg.src}}' alt='${{previewImg.alt}}'>
    //                   </div>
    //               `;

    //               counterElem.textContent = `${{currentIndex + 1}}/${{totalImages}}`;
    //           }}

    //           // 关闭灯箱
    //           function closeLightbox() {{
    //               lightboxElem.style.display = 'none';
    //           }}

    //           // 键盘支持
    //           document.addEventListener('keydown', e => {{
    //               if (lightboxElem.style.display === 'flex') {{
    //                   if (e.key === 'Escape') closeLightbox();
    //                   if (e.key === 'ArrowLeft') changeImage(-1);
    //                   if (e.key === 'ArrowRight') changeImage(1);
    //               }}
    //           }});

    //           // 分组切换
    //           function toggleGroups(btn, hiddenCount) {{
    //               const rows = btn.parentNode.querySelectorAll('.image-row[data-group]:not([data-group=""0""])');
    //               const isExpanded = rows[0] && window.getComputedStyle(rows[0]).display !== 'none';

    //               rows.forEach(row => {{
    //                   row.style.display = isExpanded ? 'none' : 'flex';
    //               }});

    //               btn.textContent = isExpanded 
    //                   ? `>> 展开更多（共 ${{hiddenCount}} 组）` 
    //                   : `<< 收起（共 ${{hiddenCount}} 组）`;
    //           }}
    //       </script>
    //   ");

    //	html.Append("</div>");
    //	return html.ToString();
    //}
    //实现了触摸左右滑动功能 但少了更多图片按钮
    //private string GenerateImageGridHtml(List<FollowRecordImage> images)
    //{
    //    if (images == null || images.Count == 0)
    //        return "<p>无关联图片</p>";

    //    // 生成唯一ID
    //    string lightboxId = "lb_" + Guid.NewGuid().ToString("N");
    //    string counterId = "cnt_" + Guid.NewGuid().ToString("N");
    //    string containerId = "ctn_" + Guid.NewGuid().ToString("N");

    //    // 创建HTML
    //    StringBuilder html = new StringBuilder();

    //    // 图片网格
    //    html.Append("<div class='image-grid'>");
    //    for (int i = 0; i < images.Count; i++)
    //    {
    //        string base64Src = ConvertToBase64(images[i].ImageData, images[i].ContentType);
    //        html.Append($@"
    //        <div class='preview-item' onclick='openLightbox({i}, ""{lightboxId}"", ""{counterId}"", ""{containerId}"", {images.Count})'>
    //            <img src='{base64Src}' alt='{images[i].FileName}'>
    //        </div>
    //    ");
    //    }
    //    html.Append("</div>");

    //    // 灯箱结构
    //    html.Append($@"
    //    <div id='{lightboxId}' class='lightbox'>
    //        <div class='lightbox-content'>
    //            <div id='{counterId}' class='lightbox-counter'>1/{images.Count}</div>
    //            <div id='{containerId}' class='lightbox-container'></div>
    //            <div class='lightbox-indicators'></div>
    //            <button class='lightbox-close' onclick='closeLightbox(""{lightboxId}"")'>×</button>
    //        </div>
    //    </div>
    //");

    //    // CSS样式
    //    html.Append(@"
    //    <style>
    //        .image-grid {
    //            display: grid;
    //            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    //            gap: 15px;
    //            margin: 20px 0;
    //        }

    //        .preview-item {
    //            height: 120px;
    //            border-radius: 8px;
    //            overflow: hidden;
    //            cursor: pointer;
    //            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    //            transition: all 0.3s;
    //        }

    //        .preview-item:hover {
    //            transform: translateY(-5px);
    //            box-shadow: 0 6px 12px rgba(0,0,0,0.25);
    //        }

    //        .preview-item img {
    //            width: 100%;
    //            height: 100%;
    //            object-fit: cover;
    //        }

    //        .lightbox {
    //            position: fixed;
    //            top: 0;
    //            left: 0;
    //            width: 100%;
    //            height: 100%;
    //            background: rgba(0,0,0,0.95);
    //            display: none;
    //            justify-content: center;
    //            align-items: center;
    //            z-index: 1000;
    //        }

    //        .lightbox-content {
    //            position: relative;
    //            width: 90%;
    //            max-width: 900px;
    //            height: 90vh;
    //        }

    //        .lightbox-container {
    //            width: 100%;
    //            height: 100%;
    //            position: relative;
    //            overflow: hidden;
    //            border-radius: 8px;
    //            background: #000;
    //        }

    //        .lightbox-slide {
    //            position: absolute;
    //            top: 0;
    //            left: 0;
    //            width: 100%;
    //            height: 100%;
    //            opacity: 0;
    //            transition: opacity 0.4s;
    //            display: flex;
    //            justify-content: center;
    //            align-items: center;
    //        }

    //        .lightbox-slide.active {
    //            opacity: 1;
    //        }

    //        .lightbox-slide img {
    //            max-width: 100%;
    //            max-height: 100%;
    //            object-fit: contain;
    //        }

    //        .lightbox-counter {
    //            position: absolute;
    //            top: 15px;
    //            left: 15px;
    //            color: white;
    //            background: rgba(0,0,0,0.5);
    //            padding: 5px 12px;
    //            border-radius: 20px;
    //            font-size: 14px;
    //            z-index: 10;
    //        }

    //        .lightbox-indicators {
    //            display: flex;
    //            justify-content: center;
    //            gap: 8px;
    //            margin-top: 15px;
    //        }

    //        .indicator {
    //            width: 10px;
    //            height: 10px;
    //            border-radius: 50%;
    //            background: rgba(255,255,255,0.4);
    //            cursor: pointer;
    //            transition: all 0.3s;
    //        }

    //        .indicator.active {
    //            background: white;
    //            transform: scale(1.2);
    //        }

    //        .lightbox-close {
    //            position: absolute;
    //            top: 15px;
    //            right: 15px;
    //            font-size: 30px;
    //            color: white;
    //            background: transparent;
    //            border: none;
    //            cursor: pointer;
    //            width: 40px;
    //            height: 40px;
    //            display: flex;
    //            justify-content: center;
    //            align-items: center;
    //            z-index: 11;
    //        }
    //    </style>
    //");

    //    // JavaScript
    //    html.Append($@"
    //    <script>
    //        let currentIndex = 0;
    //        let totalImages = 0;
    //        let lightboxElem, containerElem;
    //        let touchStartX = 0;
    //        let touchEndX = 0;

    //        function openLightbox(index, lightboxId, counterId, containerId, total) {{
    //            currentIndex = index;
    //            totalImages = total;
    //            lightboxElem = document.getElementById(lightboxId);
    //            containerElem = document.getElementById(containerId);

    //            lightboxElem.style.display = 'flex';
    //            updateLightboxImage();

    //            // 更新计数器
    //            document.getElementById(counterId).textContent = `${{index + 1}}/${{total}}`;

    //            // 添加触摸事件
    //            containerElem.addEventListener('touchstart', handleTouchStart, false);
    //            containerElem.addEventListener('touchend', handleTouchEnd, false);
    //        }}

    //        function updateLightboxImage() {{
    //            containerElem.innerHTML = '';

    //            const slide = document.createElement('div');
    //            slide.className = 'lightbox-slide active';

    //            const previewImg = document.querySelectorAll('.preview-item')[currentIndex].querySelector('img');
    //            slide.innerHTML = `<img src='${{previewImg.src}}' alt='${{previewImg.alt}}'>`;

    //            containerElem.appendChild(slide);

    //            updateIndicators();
    //        }}

    //        function updateIndicators() {{
    //            const indicators = lightboxElem.querySelector('.lightbox-indicators');
    //            indicators.innerHTML = '';

    //            for (let i = 0; i < totalImages; i++) {{
    //                const indicator = document.createElement('div');
    //                indicator.className = 'indicator' + (i === currentIndex ? ' active' : '');
    //                indicator.addEventListener('click', () => navigateToImage(i));
    //                indicators.appendChild(indicator);
    //            }}
    //        }}

    //        function navigateToImage(index) {{
    //            currentIndex = index;
    //            updateLightboxImage();
    //            lightboxElem.querySelector('.lightbox-counter').textContent = `${{index + 1}}/${{totalImages}}`;
    //        }}

    //        function closeLightbox(lightboxId) {{
    //            document.getElementById(lightboxId).style.display = 'none';

    //            // 移除事件监听器
    //            if (containerElem) {{
    //                containerElem.removeEventListener('touchstart', handleTouchStart);
    //                containerElem.removeEventListener('touchend', handleTouchEnd);
    //            }}
    //        }}

    //        function handleTouchStart(e) {{
    //            touchStartX = e.changedTouches[0].clientX;
    //        }}

    //        function handleTouchEnd(e) {{
    //            touchEndX = e.changedTouches[0].clientX;
    //            handleSwipe();
    //        }}

    //        function handleSwipe() {{
    //            const diff = touchStartX - touchEndX;
    //            const threshold = 50;

    //            if (diff > threshold) {{
    //                // 向左滑动
    //                changeImage(1);
    //            }} else if (diff < -threshold) {{
    //                // 向右滑动
    //                changeImage(-1);
    //            }}
    //        }}

    //        function changeImage(direction) {{
    //            currentIndex = (currentIndex + direction + totalImages) % totalImages;
    //            updateLightboxImage();
    //            lightboxElem.querySelector('.lightbox-counter').textContent = `${{currentIndex + 1}}/${{totalImages}}`;
    //        }}

    //        // 键盘支持
    //        document.addEventListener('keydown', function(e) {{
    //            if (!lightboxElem || lightboxElem.style.display !== 'flex') return;

    //            if (e.key === 'Escape') closeLightbox(lightboxElem.id);
    //            if (e.key === 'ArrowLeft') changeImage(-1);
    //            if (e.key === 'ArrowRight') changeImage(1);
    //        }});
    //    </script>
    //");

    //    return html.ToString();
    //}

    //实现了三组一行图片展示 也有更多图片按钮 电脑端也可适配
    //private string GenerateImageGridHtml(List<FollowRecordImage> images)
    //{
    //	if (images == null || images.Count == 0)
    //		return "<div class='no-images'>无关联图片</div>";

    //	// 创建唯一ID
    //	string lightboxId = "lb_" + Guid.NewGuid().ToString("N");
    //	string counterId = "cnt_" + Guid.NewGuid().ToString("N");
    //	string containerId = "ctn_" + Guid.NewGuid().ToString("N");

    //	// 图片分组（每组最多3张）
    //	var groups = images
    //		.Select((img, idx) => new { Img = img, Index = idx })
    //		.GroupBy(x => x.Index / 3)
    //		.ToList();

    //	StringBuilder html = new StringBuilder();
    //	html.Append("<div class='image-grid-container'>");

    //	// 图片网格
    //	for (int i = 0; i < groups.Count; i++)
    //	{
    //		var group = groups[i];
    //		string groupClass = i == 0 ? "image-group" : "image-group hidden";

    //		html.Append($"<div class='{groupClass}' data-group='{i}'>");

    //		foreach (var item in group)
    //		{
    //			string base64Src = ConvertToBase64(item.Img.ImageData, item.Img.ContentType);
    //			html.Append($@"
    //               <div class='preview-item' onclick='openLightbox({item.Index}, ""{lightboxId}"", ""{counterId}"", ""{containerId}"", {images.Count})'>
    //                   <img src='{base64Src}' alt='{item.Img.FileName}'>
    //               </div>
    //           ");
    //		}

    //		html.Append("</div>");
    //	}

    //	// 展开/收起按钮
    //	if (groups.Count > 1)
    //	{
    //		html.Append($@"
    //           <button class='toggle-groups' onclick='toggleImageGroups(this, {groups.Count})'>
    //               >> 展开更多 ({groups.Count - 1} 组)
    //           </button>
    //       ");
    //	}

    //	// 灯箱结构
    //	html.Append($@"
    //       <div id='{lightboxId}' class='lightbox'>
    //           <div class='lightbox-content'>
    //               <div id='{counterId}' class='lightbox-counter'>1/{images.Count}</div>
    //               <div id='{containerId}' class='lightbox-container'></div>
    //               <button class='lightbox-close' onclick='closeLightbox(""{lightboxId}"")'>×</button>
    //           </div>
    //       </div>
    //   ");

    //	// CSS样式
    //	html.Append(@"
    //       <style>
    //           .image-grid-container {
    //               margin: 20px 0;
    //           }

    //           .image-group {
    //               display: flex;
    //               flex-wrap: wrap;
    //               gap: 15px;
    //               margin-bottom: 20px;
    //           }

    //           .image-group.hidden {
    //               display: none;
    //           }

    //           .preview-item {
    //               flex: 0 0 calc(33.333% - 10px);
    //               max-width: calc(33.333% - 10px);
    //               height: 150px;
    //               border-radius: 10px;
    //               overflow: hidden;
    //               cursor: pointer;
    //               box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    //               transition: all 0.3s;
    //           }

    //           .preview-item:hover {
    //               transform: translateY(-5px);
    //               box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    //           }

    //           .preview-item img {
    //               width: 100%;
    //               height: 100%;
    //               object-fit: cover;
    //               transition: transform 0.5s;
    //           }

    //           .preview-item:hover img {
    //               transform: scale(1.05);
    //           }

    //           .toggle-groups {
    //               display: block;
    //               margin: 15px auto;
    //               padding: 8px 20px;
    //               background: #f0f2f5;
    //               border: 1px solid #d9d9d9;
    //               border-radius: 20px;
    //               cursor: pointer;
    //               font-size: 14px;
    //               transition: all 0.3s;
    //           }

    //           .toggle-groups:hover {
    //               background: #e6f7ff;
    //               border-color: #40a9ff;
    //               transform: translateY(-2px);
    //               box-shadow: 0 4px 8px rgba(64, 169, 255, 0.2);
    //           }

    //           /* 灯箱样式 */
    //           .lightbox {
    //               position: fixed;
    //               top: 0;
    //               left: 0;
    //               width: 100%;
    //               height: 100%;
    //               background: rgba(0,0,0,0.95);
    //               display: none;
    //               justify-content: center;
    //               align-items: center;
    //               z-index: 1000;
    //           }

    //           .lightbox-content {
    //               position: relative;
    //               width: 90%;
    //               max-width: 900px;
    //               height: 80vh;
    //           }

    //           .lightbox-container {
    //               width: 100%;
    //               height: 100%;
    //               position: relative;
    //               overflow: hidden;
    //               border-radius: 12px;
    //               background: #000;
    //           }

    //           .lightbox-slide {
    //               position: absolute;
    //               top: 0;
    //               left: 0;
    //               width: 100%;
    //               height: 100%;
    //               opacity: 0;
    //               transition: opacity 0.4s;
    //               display: flex;
    //               justify-content: center;
    //               align-items: center;
    //           }

    //           .lightbox-slide.active {
    //               opacity: 1;
    //           }

    //           .lightbox-slide img {
    //               max-width: 100%;
    //               max-height: 100%;
    //               object-fit: contain;
    //           }

    //           .lightbox-counter {
    //               position: absolute;
    //               top: 20px;
    //               left: 20px;
    //               color: white;
    //               background: rgba(0,0,0,0.5);
    //               padding: 8px 15px;
    //               border-radius: 20px;
    //               font-size: 16px;
    //               z-index: 10;
    //           }

    //           .lightbox-close {
    //               position: absolute;
    //               top: 20px;
    //               right: 20px;
    //               font-size: 30px;
    //               color: white;
    //               background: rgba(0,0,0,0.5);
    //               border: none;
    //               border-radius: 50%;
    //               width: 45px;
    //               height: 45px;
    //               cursor: pointer;
    //               z-index: 11;
    //               display: flex;
    //               justify-content: center;
    //               align-items: center;
    //               transition: all 0.3s;
    //           }

    //           .lightbox-close:hover {
    //               background: rgba(255,255,255,0.2);
    //               transform: scale(1.1);
    //           }

    //           .no-images {
    //               text-align: center;
    //               padding: 20px;
    //               color: #666;
    //               font-size: 16px;
    //           }
    //       </style>
    //   ");

    //	// JavaScript
    //	html.Append($@"
    //       <script>
    //           // 图片灯箱状态
    //           let currentIndex = 0;
    //           let totalImages = 0;
    //           let lightboxElem, containerElem;
    //           let touchStartX = 0;
    //           let touchEndX = 0;

    //           // 打开灯箱
    //           function openLightbox(index, lightboxId, counterId, containerId, total) {{
    //               currentIndex = index;
    //               totalImages = total;
    //               lightboxElem = document.getElementById(lightboxId);
    //               containerElem = document.getElementById(containerId);

    //               lightboxElem.style.display = 'flex';
    //               updateLightboxImage();

    //               // 更新计数器
    //               document.getElementById(counterId).textContent = `${{index + 1}}/${{total}}`;

    //               // 添加触摸事件
    //               containerElem.addEventListener('touchstart', handleTouchStart);
    //               containerElem.addEventListener('touchend', handleTouchEnd);
    //           }}

    //           // 更新灯箱图片
    //           function updateLightboxImage() {{
    //               containerElem.innerHTML = '';

    //               const slide = document.createElement('div');
    //               slide.className = 'lightbox-slide active';

    //               const previewItem = document.querySelectorAll('.preview-item')[currentIndex];
    //               const imgSrc = previewItem.querySelector('img').src;
    //               const imgAlt = previewItem.querySelector('img').alt;

    //               slide.innerHTML = `<img src='${{imgSrc}}' alt='${{imgAlt}}'>`;
    //               containerElem.appendChild(slide);
    //           }}

    //           // 关闭灯箱
    //           function closeLightbox(lightboxId) {{
    //               document.getElementById(lightboxId).style.display = 'none';

    //               // 移除触摸事件
    //               if (containerElem) {{
    //                   containerElem.removeEventListener('touchstart', handleTouchStart);
    //                   containerElem.removeEventListener('touchend', handleTouchEnd);
    //               }}
    //           }}

    //           // 处理触摸开始
    //           function handleTouchStart(e) {{
    //               touchStartX = e.touches[0].clientX;
    //           }}

    //           // 处理触摸结束
    //           function handleTouchEnd(e) {{
    //               touchEndX = e.changedTouches[0].clientX;
    //               handleSwipe();
    //           }}

    //           // 处理滑动操作
    //           function handleSwipe() {{
    //               const diff = touchStartX - touchEndX;
    //               const threshold = 50; // 滑动阈值

    //               if (diff > threshold) {{
    //                   // 向左滑动 - 下一张
    //                   changeImage(1);
    //               }} else if (diff < -threshold) {{
    //                   // 向右滑动 - 上一张
    //                   changeImage(-1);
    //               }}
    //           }}

    //           // 切换图片
    //           function changeImage(direction) {{
    //               currentIndex = (currentIndex + direction + totalImages) % totalImages;
    //               updateLightboxImage();
    //               lightboxElem.querySelector('.lightbox-counter').textContent = `${{currentIndex + 1}}/${{totalImages}}`;
    //           }}

    //           // 键盘支持
    //           document.addEventListener('keydown', function(e) {{
    //               if (!lightboxElem || lightboxElem.style.display !== 'flex') return;

    //               if (e.key === 'Escape') closeLightbox(lightboxElem.id);
    //               if (e.key === 'ArrowLeft') changeImage(-1);
    //               if (e.key === 'ArrowRight') changeImage(1);
    //           }});

    //           // 切换图片组展开/收起
    //           function toggleImageGroups(button, groupCount) {{
    //               const groups = document.querySelectorAll('.image-group[data-group]');
    //               const isExpanded = groups[1] && window.getComputedStyle(groups[1]).display !== 'none';

    //               // 切换所有隐藏组的显示状态
    //               for (let i = 1; i < groups.length; i++) {{
    //                   groups[i].style.display = isExpanded ? 'none' : 'flex';
    //               }}

    //               // 更新按钮文本
    //               button.innerHTML = isExpanded 
    //                   ? `>> 展开更多 (${{groupCount - 1}} 组)` 
    //                   : `<< 收起 (${{groupCount - 1}} 组)`;
    //           }}
    //       </script>
    //   ");

    //	html.Append("</div>");
    //	return html.ToString();
    //}
    //实现了点击全屏后左右翻图按钮的实现 还有外面图片每行三张
    //private string GenerateImageGridHtml(List<FollowRecordImage> images)
    //{
    //	if (images == null || images.Count == 0)
    //		return "<div class='no-images'>无关联图片</div>";

    //	// 创建唯一ID
    //	string lightboxId = "lb_" + Guid.NewGuid().ToString("N");
    //	string counterId = "cnt_" + Guid.NewGuid().ToString("N");
    //	string containerId = "ctn_" + Guid.NewGuid().ToString("N");
    //	string dotsId = "dots_" + Guid.NewGuid().ToString("N"); // 圆点指示器容器ID
    //	string imgContainerId = "imgCtn_" + Guid.NewGuid().ToString("N"); // 图片容器唯一ID


    //	// 图片分组（每组最多3张）
    //	var groups = images
    //		.Select((img, idx) => new { Img = img, Index = idx })
    //		.GroupBy(x => x.Index / 3)
    //		.ToList();

    //	StringBuilder html = new StringBuilder();
    //	html.Append("<div id='{imgContainerId}' class='image-grid-container'>");

    //	// 图片网格
    //	for (int i = 0; i < groups.Count; i++)
    //	{
    //		var group = groups[i];
    //		string groupClass = i == 0 ? "image-group" : "image-group hidden";

    //		html.Append($"<div class='{groupClass}' data-group='{i}'>");

    //		foreach (var item in group)
    //		{
    //			string base64Src = ConvertToBase64(item.Img.ImageData, item.Img.ContentType);
    //			html.Append($@"
    //            <div class='preview-item' onclick='event.preventDefault();openLightbox({item.Index}, ""{lightboxId}"", ""{counterId}"", ""{containerId}"", ""{dotsId}"", {images.Count})'>
    //                <img src='{base64Src}' alt='{item.Img.FileName}'>
    //            </div>
    //        ");
    //		}

    //		html.Append("</div>");
    //	}

    //	// 展开/收起按钮
    //	if (groups.Count > 1)
    //	{
    //		html.Append($@"
    //                  <button class='toggle-groups' onclick='event.preventDefault();toggleImageGroups(""{imgContainerId}"", this, {groups.Count})'>
    //                      >> 展开更多 ({groups.Count - 1} 组)
    //                  </button>
    //              ");
    //	}

    //	// 灯箱结构 - 添加导航按钮和圆点指示器
    //	html.Append($@"
    //    <div id='{lightboxId}' class='lightbox'>
    //        <div class='lightbox-content'>
    //            <div id='{counterId}' class='lightbox-counter'>1/{images.Count}</div>

    //            <!-- 导航按钮 -->
    //            <button class='nav-btn prev-btn' onclick='event.preventDefault();changeImage(-1, ""{lightboxId}"", ""{counterId}"", ""{containerId}"", ""{dotsId}"", {images.Count})'>
    //                <i class='fas fa-chevron-left'></i>
    //            </button>

    //            <div id='{containerId}' class='lightbox-container'></div>

    //            <button class='nav-btn next-btn' onclick='event.preventDefault();changeImage(1, ""{lightboxId}"", ""{counterId}"", ""{containerId}"", ""{dotsId}"", {images.Count})'>
    //                <i class='fas fa-chevron-right'></i>
    //            </button>

    //            <!-- 圆点指示器 -->
    //            <div id='{dotsId}' class='lightbox-dots'></div>

    //            <button class='lightbox-close' onclick='event.preventDefault();closeLightbox(""{lightboxId}"")'>
    //                <i class='fas fa-times'></i>
    //            </button>
    //        </div>
    //    </div>
    //");

    //	// CSS样式 - 添加导航按钮和圆点样式
    //	html.Append(@"
    //    <style>
    //        /* 引入Font Awesome图标 */
    //        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

    //        .image-grid-container {
    //            margin: 20px 0;
    //        }

    //        .image-group {
    //            display: flex;
    //            flex-wrap: wrap;
    //            gap: 15px;
    //            margin-bottom: 20px;
    //        }

    //        .image-group.hidden {
    //            display: none;
    //        }

    //        .preview-item {
    //            flex: 0 0 calc(33.333% - 10px);
    //            max-width: calc(33.333% - 10px);
    //            height: 150px;
    //            border-radius: 10px;
    //            overflow: hidden;
    //            cursor: pointer;
    //            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    //            transition: all 0.3s;
    //        }

    //        .preview-item:hover {
    //            transform: translateY(-5px);
    //            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    //        }

    //        .preview-item img {
    //            width: 100%;
    //            height: 100%;
    //            object-fit: cover;
    //            transition: transform 0.5s;
    //        }

    //        .preview-item:hover img {
    //            transform: scale(1.05);
    //        }

    //        .toggle-groups {
    //            display: block;
    //            margin: 15px auto;
    //            padding: 8px 20px;
    //            background: #f0f2f5;
    //            border: 1px solid #d9d9d9;
    //            border-radius: 20px;
    //            cursor: pointer;
    //            font-size: 14px;
    //            transition: all 0.3s;
    //        }

    //        .toggle-groups:hover {
    //            background: #e6f7ff;
    //            border-color: #40a9ff;
    //            transform: translateY(-2px);
    //            box-shadow: 0 4px 8px rgba(64, 169, 255, 0.2);
    //        }

    //        /* 灯箱样式 */
    //        .lightbox {
    //            position: fixed;
    //            top: 0;
    //            left: 0;
    //            width: 100%;
    //            height: 100%;
    //            background: rgba(0,0,0,0.95);
    //            display: none;
    //            justify-content: center;
    //            align-items: center;
    //            z-index: 1000;
    //        }

    //        .lightbox-content {
    //            position: relative;
    //            width: 90%;
    //            max-width: 900px;
    //            height: 80vh;
    //            display: flex;
    //            flex-direction: column;
    //            align-items: center;
    //        }

    //        .lightbox-container {
    //            width: 100%;
    //            height: 100%;
    //            position: relative;
    //            overflow: hidden;
    //            border-radius: 12px;
    //            background: #000;
    //        }

    //        .lightbox-slide {
    //            position: absolute;
    //            top: 0;
    //            left: 0;
    //            width: 100%;
    //            height: 100%;
    //            opacity: 0;
    //            transition: opacity 0.4s;
    //            display: flex;
    //            justify-content: center;
    //            align-items: center;
    //        }

    //        .lightbox-slide.active {
    //            opacity: 1;
    //        }

    //        .lightbox-slide img {
    //            max-width: 100%;
    //            max-height: 100%;
    //            object-fit: contain;
    //        }

    //        .lightbox-counter {
    //            position: absolute;
    //            top: 20px;
    //            left: 20px;
    //            color: white;
    //            background: rgba(0,0,0,0.5);
    //            padding: 8px 15px;
    //            border-radius: 20px;
    //            font-size: 16px;
    //            z-index: 10;
    //        }

    //        /* 导航按钮样式 */
    //        .nav-btn {
    //            position: absolute;
    //            top: 50%;
    //            transform: translateY(-50%);
    //            background: rgba(255,255,255,0.2);
    //            border: none;
    //            width: 60px;
    //            height: 60px;
    //            border-radius: 50%;
    //            display: flex;
    //            justify-content: center;
    //            align-items: center;
    //            cursor: pointer;
    //            transition: all 0.3s;
    //            z-index: 11;
    //        }

    //        .nav-btn i {
    //            font-size: 24px;
    //            color: white;
    //        }

    //        .nav-btn:hover {
    //            background: rgba(255,255,255,0.3);
    //            transform: translateY(-50%) scale(1.1);
    //        }

    //        .prev-btn {
    //            left: 20px;
    //        }

    //        .next-btn {
    //            right: 20px;
    //        }

    //        /* 圆点指示器 */
    //        .lightbox-dots {
    //            position: absolute;
    //            bottom: 20px;
    //            left: 0;
    //            right: 0;
    //            display: flex;
    //            justify-content: center;
    //            gap: 10px;
    //            z-index: 10;
    //        }

    //        .dot {
    //            width: 12px;
    //            height: 12px;
    //            border-radius: 50%;
    //            background: rgba(255,255,255,0.4);
    //            cursor: pointer;
    //            transition: all 0.3s;
    //        }

    //        .dot.active {
    //            background: white;
    //            transform: scale(1.2);
    //        }

    //        .lightbox-close {
    //            position: absolute;
    //            top: 20px;
    //            right: 20px;
    //            font-size: 24px;
    //            color: white;
    //            background: rgba(0,0,0,0.5);
    //            border: none;
    //            border-radius: 50%;
    //            width: 45px;
    //            height: 45px;
    //            cursor: pointer;
    //            z-index: 11;
    //            display: flex;
    //            justify-content: center;
    //            align-items: center;
    //            transition: all 0.3s;
    //        }

    //        .lightbox-close i {
    //            font-size: 20px;
    //        }

    //        .lightbox-close:hover {
    //            background: rgba(255,255,255,0.2);
    //            transform: scale(1.1);
    //        }

    //        .no-images {
    //            text-align: center;
    //            padding: 20px;
    //            color: #666;
    //            font-size: 16px;
    //        }

    //        /* 响应式调整 */
    //        @media (max-width: 768px) {
    //            .preview-item {
    //                height: 120px;
    //            }

    //            .nav-btn {
    //                width: 40px;
    //                height: 40px;
    //            }

    //            .nav-btn i {
    //                font-size: 18px;
    //            }
    //        }
    //    </style>
    //");

    //	// JavaScript - 添加导航和圆点功能
    //	html.Append($@"
    //    <script>
    //        // 图片灯箱状态
    //        let currentIndex = 0;
    //        let totalImages = 0;
    //        let lightboxElem, containerElem, dotsContainer;
    //        let touchStartX = 0;
    //        let touchEndX = 0;

    //        // 打开灯箱
    //        function openLightbox(index, lightboxId, counterId, containerId, dotsId, total) {{
    //            currentIndex = index;
    //            totalImages = total;
    //            lightboxElem = document.getElementById(lightboxId);
    //            containerElem = document.getElementById(containerId);
    //            dotsContainer = document.getElementById(dotsId);

    //            lightboxElem.style.display = 'flex';
    //            updateLightboxImage();
    //            createDots();

    //            // 更新计数器
    //            document.getElementById(counterId).textContent = `${{index + 1}}/${{total}}`;

    //            // 添加触摸事件
    //            containerElem.addEventListener('touchstart', handleTouchStart);
    //            containerElem.addEventListener('touchend', handleTouchEnd);
    //        }}

    //        // 更新灯箱图片
    //        function updateLightboxImage() {{
    //            containerElem.innerHTML = '';

    //            const slide = document.createElement('div');
    //            slide.className = 'lightbox-slide active';

    //            const previewItem = document.querySelectorAll('.preview-item')[currentIndex];
    //            const imgSrc = previewItem.querySelector('img').src;
    //            const imgAlt = previewItem.querySelector('img').alt;

    //            slide.innerHTML = `<img src='${{imgSrc}}' alt='${{imgAlt}}'>`;
    //            containerElem.appendChild(slide);
    //        }}

    //        // 创建小圆点指示器
    //        function createDots() {{
    //            dotsContainer.innerHTML = '';

    //            for (let i = 0; i < totalImages; i++) {{
    //                const dot = document.createElement('div');
    //                dot.className = 'dot' + (i === currentIndex ? ' active' : '');
    //                dot.addEventListener('click', () => navigateToImage(i));
    //                dotsContainer.appendChild(dot);
    //            }}
    //        }}

    //        // 导航到指定图片
    //        function navigateToImage(index) {{
    //            currentIndex = index;
    //            updateLightboxImage();
    //            updateDots();
    //            lightboxElem.querySelector('.lightbox-counter').textContent = `${{index + 1}}/${{totalImages}}`;
    //        }}

    //        // 更新小圆点状态
    //        function updateDots() {{
    //            const dots = dotsContainer.querySelectorAll('.dot');
    //            dots.forEach((dot, index) => {{
    //                dot.classList.toggle('active', index === currentIndex);
    //            }});
    //        }}

    //        // 关闭灯箱
    //        function closeLightbox(lightboxId) {{
    //            document.getElementById(lightboxId).style.display = 'none';

    //            // 移除触摸事件
    //            if (containerElem) {{
    //                containerElem.removeEventListener('touchstart', handleTouchStart);
    //                containerElem.removeEventListener('touchend', handleTouchEnd);
    //            }}
    //        }}

    //        // 处理触摸开始
    //        function handleTouchStart(e) {{
    //            touchStartX = e.touches[0].clientX;
    //        }}

    //        // 处理触摸结束
    //        function handleTouchEnd(e) {{
    //            touchEndX = e.changedTouches[0].clientX;
    //            handleSwipe();
    //        }}

    //        // 处理滑动操作
    //        function handleSwipe() {{
    //            const diff = touchStartX - touchEndX;
    //            const threshold = 50; // 滑动阈值

    //            if (diff > threshold) {{
    //                // 向左滑动 - 下一张
    //                changeImage(1, lightboxElem.id, '', containerElem.id, dotsContainer.id, totalImages);
    //            }} else if (diff < -threshold) {{
    //                // 向右滑动 - 上一张
    //                changeImage(-1, lightboxElem.id, '', containerElem.id, dotsContainer.id, totalImages);
    //            }}
    //        }}

    //        // 切换图片
    //        function changeImage(direction, lightboxId, counterId, containerId, dotsId, total) {{
    //            currentIndex = (currentIndex + direction + total) % total;
    //            updateLightboxImage();
    //            updateDots();
    //            lightboxElem.querySelector('.lightbox-counter').textContent = `${{currentIndex + 1}}/${{total}}`;
    //        }}

    //        // 键盘支持
    //        document.addEventListener('keydown', function(e) {{
    //            if (!lightboxElem || lightboxElem.style.display !== 'flex') return;

    //            if (e.key === 'Escape') closeLightbox(lightboxElem.id);
    //            if (e.key === 'ArrowLeft') changeImage(-1, lightboxElem.id, '', containerElem.id, dotsContainer.id, totalImages);
    //            if (e.key === 'ArrowRight') changeImage(1, lightboxElem.id, '', containerElem.id, dotsContainer.id, totalImages);
    //        }});

    //            // 切换图片组展开/收起（作用域限定在当前容器）
    //           function toggleImageGroups(containerId, button, groupCount) {{
    //               const container = document.getElementById(containerId);
    //               const groups = container.querySelectorAll('.image-group[data-group]');
    //               const isExpanded = groups[1] && window.getComputedStyle(groups[1]).display !== 'none';

    //               // 切换所有隐藏组的显示状态
    //               for (let i = 1; i < groups.length; i++) {{
    //                   groups[i].style.display = isExpanded ? 'none' : 'flex';
    //               }}

    //               // 更新按钮文本
    //               button.innerHTML = isExpanded 
    //                   ? `>> 展开更多 (${{groupCount - 1}} 组)` 
    //                   : `<< 收起 (${{groupCount - 1}} 组)`;
    //           }}
    //    </script>
    //");

    //	html.Append("</div>");
    //	return html.ToString();
    //}
    //private string GenerateImageGridHtml(List<FollowRecordImage> images)
    //{
    //	if (images == null || images.Count == 0)
    //		return "<div class='no-images'>无关联图片</div>";

    //	// 创建唯一ID
    //	string containerId = "ctr_" + Guid.NewGuid().ToString("N");
    //	string lightboxId = "lb_" + Guid.NewGuid().ToString("N");
    //	string counterId = "cnt_" + Guid.NewGuid().ToString("N");
    //	string slidesId = "sld_" + Guid.NewGuid().ToString("N");
    //	string dotsId = "dots_" + Guid.NewGuid().ToString("N");

    //	// 图片分组（每组最多3张）
    //	var groups = images
    //		.Select((img, idx) => new { Img = img, Index = idx })
    //		.GroupBy(x => x.Index / 3)
    //		.ToList();

    //	StringBuilder html = new StringBuilder();
    //	html.Append($"<div id='{containerId}' class='image-grid-container'>");

    //	// 图片网格
    //	for (int i = 0; i < groups.Count; i++)
    //	{
    //		var group = groups[i];
    //		string groupClass = i == 0 ? "image-group" : "image-group hidden";

    //		html.Append($"<div class='{groupClass}' data-group='{i}'>");

    //		foreach (var item in group)
    //		{
    //			string base64Src = ConvertToBase64(item.Img.ImageData, item.Img.ContentType);
    //			html.Append($@"
    //            <div class='preview-item' onclick='openLightbox({item.Index}, ""{lightboxId}"", ""{counterId}"", ""{slidesId}"", ""{dotsId}"", {images.Count})'>
    //                <img src='{base64Src}' alt='{item.Img.FileName}'>
    //            </div>
    //        ");
    //		}

    //		html.Append("</div>");
    //	}

    //	// 展开/收起按钮（只影响当前组）
    //	if (groups.Count > 1)
    //	{
    //		html.Append($@"
    //        <button class='toggle-groups' onclick='event.preventDefault();toggleImageGroups(""{containerId}"", this, {groups.Count})'>
    //            <span class='toggle-text'>> 展开更多 ({groups.Count - 1} 组)</span>
    //            <span class='toggle-icon'></span>
    //        </button>
    //    ");
    //	}

    //	// 灯箱结构 - 自定义导航按钮
    //	html.Append($@"
    //    <div id='{lightboxId}' class='lightbox'>
    //        <div class='lightbox-content'>
    //            <div id='{counterId}' class='lightbox-counter'>1/{images.Count}</div>

    //            <!-- 自定义导航按钮 -->
    //            <button class='nav-btn prev-btn' onclick='changeImage(-1, ""{slidesId}"", ""{dotsId}"", ""{counterId}"", {images.Count})'>
    //                <div class='arrow-left'></div>
    //            </button>

    //            <div id='{slidesId}' class='lightbox-slides'></div>

    //            <button class='nav-btn next-btn' onclick='changeImage(1, ""{slidesId}"", ""{dotsId}"", ""{counterId}"", {images.Count})'>
    //                <div class='arrow-right'></div>
    //            </button>

    //            <!-- 圆点指示器 -->
    //            <div id='{dotsId}' class='lightbox-dots'></div>

    //            <button class='lightbox-close' onclick='closeLightbox(""{lightboxId}"")'>
    //                <div class='close-icon'></div>
    //            </button>
    //        </div>
    //    </div>
    //");

    //	// CSS样式 - 自定义图标和布局
    //	html.Append(@"
    //    <style>
    //        .image-grid-container {
    //            margin: 20px 0;
    //            position: relative;
    //        }

    //        .image-group {
    //            display: flex;
    //            flex-wrap: wrap;
    //            gap: 15px;
    //            margin-bottom: 20px;
    //        }

    //        .image-group.hidden {
    //            display: none;
    //        }

    //        .preview-item {
    //            flex: 0 0 calc(33.333% - 10px);
    //            border-radius: 10px;
    //            overflow: hidden;
    //            cursor: pointer;
    //            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    //            transition: all 0.3s;
    //            aspect-ratio: 4/3; /* 保持图片比例 */
    //        }

    //        .preview-item:hover {
    //            transform: translateY(-5px);
    //            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    //        }

    //        .preview-item img {
    //            width: 100%;
    //            height: 100%;
    //            object-fit: cover;
    //            transition: transform 0.5s;
    //        }

    //        .preview-item:hover img {
    //            transform: scale(1.05);
    //        }

    //        .toggle-groups {
    //            display: flex;
    //            align-items: center;
    //            margin: 15px auto;
    //            padding: 8px 15px;
    //            background: #f0f2f5;
    //            border: 1px solid #d9d9d9;
    //            border-radius: 20px;
    //            cursor: pointer;
    //            font-size: 14px;
    //            transition: all 0.3s;
    //            position: relative;
    //        }

    //        .toggle-groups:hover {
    //            background: #e6f7ff;
    //            border-color: #40a9ff;
    //            transform: translateY(-2px);
    //            box-shadow: 0 4px 8px rgba(64, 169, 255, 0.2);
    //        }

    //        .toggle-icon {
    //            display: inline-block;
    //            width: 20px;
    //            height: 20px;
    //            margin-left: 8px;
    //            position: relative;
    //        }

    //        .toggle-icon::before, .toggle-icon::after {
    //            content: '';
    //            position: absolute;
    //            width: 10px;
    //            height: 2px;
    //            background: #333;
    //            top: 50%;
    //            transition: all 0.3s;
    //        }

    //        .toggle-icon::before {
    //            right: 5px;
    //            transform: translateY(-50%) rotate(45deg);
    //        }

    //        .toggle-icon::after {
    //            right: 0;
    //            transform: translateY(-50%) rotate(-45deg);
    //        }

    //        .toggle-groups.expanded .toggle-icon::before {
    //            transform: translateY(-50%) rotate(-45deg);
    //        }

    //        .toggle-groups.expanded .toggle-icon::after {
    //            transform: translateY(-50%) rotate(45deg);
    //        }

    //        /* 灯箱样式 */
    //        .lightbox {
    //            position: fixed;
    //            top: 0;
    //            left: 0;
    //            width: 100%;
    //            height: 100%;
    //            background: rgba(0,0,0,0.95);
    //            display: none;
    //            justify-content: center;
    //            align-items: center;
    //            z-index: 1000;
    //        }

    //        .lightbox-content {
    //            position: relative;
    //            width: 90%;
    //            max-width: 900px;
    //            height: 80vh;
    //            display: flex;
    //            flex-direction: column;
    //            align-items: center;
    //        }

    //        .lightbox-slides {
    //            width: 100%;
    //            height: 100%;
    //            position: relative;
    //            overflow: hidden;
    //            border-radius: 12px;
    //            background: #000;
    //        }

    //        .lightbox-slide {
    //            position: absolute;
    //            top: 0;
    //            left: 0;
    //            width: 100%;
    //            height: 100%;
    //            opacity: 0;
    //            transition: opacity 0.4s;
    //            display: flex;
    //            justify-content: center;
    //            align-items: center;
    //        }

    //        .lightbox-slide.active {
    //            opacity: 1;
    //        }

    //        .lightbox-slide img {
    //            max-width: 100%;
    //            max-height: 100%;
    //            object-fit: contain;
    //        }

    //        .lightbox-counter {
    //            position: absolute;
    //            top: 20px;
    //            left: 20px;
    //            color: white;
    //            background: rgba(0,0,0,0.5);
    //            padding: 8px 15px;
    //            border-radius: 20px;
    //            font-size: 16px;
    //            z-index: 10;
    //        }

    //        /* 自定义导航按钮 */
    //        .nav-btn {
    //            position: absolute;
    //            top: 50%;
    //            transform: translateY(-50%);
    //            background: rgba(255,255,255,0.2);
    //            border: none;
    //            width: 60px;
    //            height: 60px;
    //            border-radius: 50%;
    //            display: flex;
    //            justify-content: center;
    //            align-items: center;
    //            cursor: pointer;
    //            transition: all 0.3s;
    //            z-index: 11;
    //        }

    //        .nav-btn:hover {
    //            background: rgba(255,255,255,0.3);
    //            transform: translateY(-50%) scale(1.1);
    //        }

    //        .prev-btn {
    //            left: 20px;
    //        }

    //        .next-btn {
    //            right: 20px;
    //        }

    //        .arrow-left, .arrow-right {
    //            width: 25px;
    //            height: 25px;
    //            border-top: 3px solid white;
    //            border-left: 3px solid white;
    //            position: relative;
    //        }

    //        .arrow-left {
    //            transform: rotate(-45deg);
    //            margin-right: 5px;
    //        }

    //        .arrow-right {
    //            transform: rotate(135deg);
    //            margin-left: 5px;
    //        }

    //        /* 自定义关闭按钮 */
    //        .close-icon {
    //            position: relative;
    //            width: 25px;
    //            height: 25px;
    //        }

    //        .close-icon::before, .close-icon::after {
    //            content: '';
    //            position: absolute;
    //            top: 50%;
    //            left: 0;
    //            width: 100%;
    //            height: 2px;
    //            background: white;
    //        }

    //        .close-icon::before {
    //            transform: rotate(45deg);
    //        }

    //        .close-icon::after {
    //            transform: rotate(-45deg);
    //        }

    //        .lightbox-close {
    //            position: absolute;
    //            top: 20px;
    //            right: 20px;
    //            background: rgba(0,0,0,0.5);
    //            border: none;
    //            border-radius: 50%;
    //            width: 45px;
    //            height: 45px;
    //            cursor: pointer;
    //            z-index: 11;
    //            display: flex;
    //            justify-content: center;
    //            align-items: center;
    //            transition: all 0.3s;
    //        }

    //        .lightbox-close:hover {
    //            background: rgba(255,255,255,0.2);
    //            transform: scale(1.1);
    //        }

    //        /* 圆点指示器 */
    //        .lightbox-dots {
    //            position: absolute;
    //            bottom: 20px;
    //            left: 0;
    //            right: 0;
    //            display: flex;
    //            justify-content: center;
    //            gap: 10px;
    //            z-index: 10;
    //        }

    //        .dot {
    //            width: 12px;
    //            height: 12px;
    //            border-radius: 50%;
    //            background: rgba(255,255,255,0.4);
    //            cursor: pointer;
    //            transition: all 0.3s;
    //        }

    //        .dot.active {
    //            background: white;
    //            transform: scale(1.2);
    //        }

    //        .no-images {
    //            text-align: center;
    //            padding: 20px;
    //            color: #666;
    //            font-size: 16px;
    //        }

    //        /* 响应式调整 */
    //        @media (max-width: 768px) {
    //            .preview-item {
    //                flex: 0 0 calc(50% - 10px);
    //                height: 120px;
    //            }

    //            .nav-btn {
    //                width: 40px;
    //                height: 40px;
    //            }

    //            .arrow-left, .arrow-right {
    //                width: 18px;
    //                height: 18px;
    //                border-width: 2px;
    //            }
    //        }

    //        @media (max-width: 480px) {
    //            .preview-item {
    //                flex: 0 0 100%;
    //                height: 160px;
    //            }
    //        }
    //    </style>
    //");

    //	// JavaScript - 添加导航和分组功能
    //	html.Append($@"
    //    <script>
    //        // 图片灯箱状态
    //        let currentIndex = 0;
    //        let totalImages = 0;
    //        let lightboxElem, slidesContainer, dotsContainer;
    //        let touchStartX = 0;
    //        let touchEndX = 0;

    //        // 打开灯箱
    //        function openLightbox(index, lightboxId, counterId, slidesId, dotsId, total) {{
    //            currentIndex = index;
    //            totalImages = total;
    //            lightboxElem = document.getElementById(lightboxId);
    //            slidesContainer = document.getElementById(slidesId);
    //            dotsContainer = document.getElementById(dotsId);

    //            lightboxElem.style.display = 'flex';
    //            updateLightboxImage();
    //            createDots();

    //            // 更新计数器
    //            document.getElementById(counterId).textContent = `${{index + 1}}/${{total}}`;

    //            // 添加触摸事件
    //            slidesContainer.addEventListener('touchstart', handleTouchStart);
    //            slidesContainer.addEventListener('touchend', handleTouchEnd);
    //        }}

    //        // 更新灯箱图片
    //        function updateLightboxImage() {{
    //            slidesContainer.innerHTML = '';

    //            const slide = document.createElement('div');
    //            slide.className = 'lightbox-slide active';

    //            const previewItem = document.querySelectorAll('.preview-item')[currentIndex];
    //            const imgSrc = previewItem.querySelector('img').src;
    //            const imgAlt = previewItem.querySelector('img').alt;

    //            slide.innerHTML = `<img src='${{imgSrc}}' alt='${{imgAlt}}'>`;
    //            slidesContainer.appendChild(slide);
    //        }}

    //        // 创建小圆点指示器
    //        function createDots() {{
    //            dotsContainer.innerHTML = '';

    //            for (let i = 0; i < totalImages; i++) {{
    //                const dot = document.createElement('div');
    //                dot.className = 'dot' + (i === currentIndex ? ' active' : '');
    //                dot.addEventListener('click', () => navigateToImage(i));
    //                dotsContainer.appendChild(dot);
    //            }}
    //        }}

    //        // 导航到指定图片
    //        function navigateToImage(index) {{
    //            currentIndex = index;
    //            updateLightboxImage();
    //            updateDots();
    //            document.querySelector('.lightbox-counter').textContent = `${{index + 1}}/${{totalImages}}`;
    //        }}

    //        // 更新小圆点状态
    //        function updateDots() {{
    //            const dots = dotsContainer.querySelectorAll('.dot');
    //            dots.forEach((dot, index) => {{
    //                dot.classList.toggle('active', index === currentIndex);
    //            }});
    //        }}

    //        // 关闭灯箱
    //        function closeLightbox(lightboxId) {{
    //            document.getElementById(lightboxId).style.display = 'none';

    //            // 移除触摸事件
    //            if (slidesContainer) {{
    //                slidesContainer.removeEventListener('touchstart', handleTouchStart);
    //                slidesContainer.removeEventListener('touchend', handleTouchEnd);
    //            }}
    //        }}

    //        // 处理触摸开始
    //        function handleTouchStart(e) {{
    //            touchStartX = e.touches[0].clientX;
    //        }}

    //        // 处理触摸结束
    //        function handleTouchEnd(e) {{
    //            touchEndX = e.changedTouches[0].clientX;
    //            handleSwipe();
    //        }}

    //        // 处理滑动操作
    //        function handleSwipe() {{
    //            const diff = touchStartX - touchEndX;
    //            const threshold = 50; // 滑动阈值

    //            if (diff > threshold) {{
    //                // 向左滑动 - 下一张
    //                changeImage(1, slidesContainer.id, dotsContainer.id, '.lightbox-counter', totalImages);
    //            }} else if (diff < -threshold) {{
    //                // 向右滑动 - 上一张
    //                changeImage(-1, slidesContainer.id, dotsContainer.id, '.lightbox-counter', totalImages);
    //            }}
    //        }}

    //        // 切换图片
    //        function changeImage(direction, slidesId, dotsId, counterSelector, total) {{
    //            currentIndex = (currentIndex + direction + total) % total;
    //            const slidesContainer = document.getElementById(slidesId);
    //            const dotsContainer = document.getElementById(dotsId);

    //            slidesContainer.innerHTML = '';

    //            const slide = document.createElement('div');
    //            slide.className = 'lightbox-slide active';

    //            const previewItem = document.querySelectorAll('.preview-item')[currentIndex];
    //            const imgSrc = previewItem.querySelector('img').src;
    //            const imgAlt = previewItem.querySelector('img').alt;

    //            slide.innerHTML = `<img src='${{imgSrc}}' alt='${{imgAlt}}'>`;
    //            slidesContainer.appendChild(slide);

    //            // 更新圆点
    //            const dots = dotsContainer.querySelectorAll('.dot');
    //            dots.forEach((dot, index) => {{
    //                dot.classList.toggle('active', index === currentIndex);
    //            }});

    //            // 更新计数器
    //            document.querySelector(counterSelector).textContent = `${{currentIndex + 1}}/${{total}}`;
    //        }}

    //        // 键盘支持
    //        document.addEventListener('keydown', function(e) {{
    //            if (!lightboxElem || lightboxElem.style.display !== 'flex') return;

    //            if (e.key === 'Escape') closeLightbox(lightboxElem.id);
    //            if (e.key === 'ArrowLeft') changeImage(-1, slidesContainer.id, dotsContainer.id, '.lightbox-counter', totalImages);
    //            if (e.key === 'ArrowRight') changeImage(1, slidesContainer.id, dotsContainer.id, '.lightbox-counter', totalImages);
    //        }});

    //        // 切换图片组展开/收起（仅影响当前记录）
    //        function toggleImageGroups(containerId, button, groupCount) {{
    //            const container = document.getElementById(containerId);
    //            const groups = container.querySelectorAll('.image-group[data-group]');
    //            const isExpanded = groups[1] && window.getComputedStyle(groups[1]).display !== 'none';

    //            // 切换所有隐藏组的显示状态
    //            for (let i = 1; i < groups.length; i++) {{
    //                groups[i].style.display = isExpanded ? 'none' : 'flex';
    //            }}

    //            // 更新按钮文本和样式
    //            const toggleText = button.querySelector('.toggle-text');
    //            toggleText.innerHTML = isExpanded 
    //                ? `>> 展开更多 (${{groupCount - 1}} 组)` 
    //                : `<< 收起 (${{groupCount - 1}} 组)`;

    //            // 切换按钮状态
    //            if (isExpanded) {{
    //                button.classList.remove('expanded');
    //            }} else {{
    //                button.classList.add('expanded');
    //            }}
    //        }}
    //    </script>
    //");

    //	html.Append("</div>");
    //	return html.ToString();
    //}
    //18:50

    //private string GenerateImageGridHtml(List<FollowRecordImage> images)
    //{
    //	if (images == null || images.Count == 0)
    //		return "<div class='no-images'>无关联图片</div>";

    //	// 创建唯一ID
    //	string lightboxId = "lb_" + Guid.NewGuid().ToString("N");
    //	string counterId = "cnt_" + Guid.NewGuid().ToString("N");
    //	string containerId = "ctn_" + Guid.NewGuid().ToString("N");
    //	string dotsId = "dots_" + Guid.NewGuid().ToString("N");
    //	string imgContainerId = "imgCtn_" + Guid.NewGuid().ToString("N"); // 图片容器唯一ID

    //	// 图片分组（每组最多3张）
    //	var groups = images
    //		.Select((img, idx) => new { Img = img, Index = idx })
    //		.GroupBy(x => x.Index / 3)
    //		.ToList();

    //	StringBuilder html = new StringBuilder();
    //	html.Append($"<div id='{imgContainerId}' class='image-grid-container'>");

    //	// 图片网格
    //	for (int i = 0; i < groups.Count; i++)
    //	{
    //		var group = groups[i];
    //		string groupClass = i == 0 ? "image-group" : "image-group hidden";

    //		html.Append($"<div class='{groupClass}' data-group='{i}'>");

    //		int imageCount = 0;
    //		//html.Append($@"<div class='preview-item'>");
    //		foreach (var item in group)
    //		{
    //			////每行满3张则换行
    //			//if (imageCount > 0 && imageCount % 3 == 0) {
    //			//    html.Append( "<br/>");
    //			//}                        style='max-width:26vw; max-height:100px; margin:4px;'>

    //			string base64Src = ConvertToBase64(item.Img.ImageData, item.Img.ContentType);
    //			html.Append($@"
    //                   <div class='preview-item' onclick='event.preventDefault();openLightbox({item.Index}, ""{lightboxId}"", ""{counterId}"", ""{containerId}"", ""{dotsId}"", {images.Count})'>
    //                   <img 
    //                       src='{base64Src}' 
    //                       alt='{item.Img.FileName}'>
    //                    </div>
    //           ");
    //			imageCount++;
    //		}

    //		html.Append("</div>");
    //	}

    //	// 展开/收起按钮（作用域限定在当前容器）
    //	if (groups.Count > 1)
    //	{
    //		html.Append($@"
    //           <button class='toggle-groups' onclick='event.preventDefault();toggleImageGroups(""{imgContainerId}"", this, {groups.Count})'>
    //               >> 展开更多 ({groups.Count - 1} 组)
    //           </button>
    //       ");
    //	}

    //	// 灯箱结构 - 使用自定义SVG图标
    //	html.Append($@"
    //       <div id='{lightboxId}' class='lightbox'>
    //           <div class='lightbox-content'>
    //               <div id='{counterId}' class='lightbox-counter'>1/{images.Count}</div>

    //               <!-- 自定义导航按钮 -->
    //               <button class='navigation-button prev-btn' onclick='event.preventDefault();changeImage(-1, ""{lightboxId}"", ""{counterId}"", ""{containerId}"", ""{dotsId}"", {images.Count})'>
    //                   <svg width='40' height='40' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
    //                       <path d='M15 18L9 12L15 6' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/>
    //                   </svg>
    //               </button>

    //               <div id='{containerId}' class='lightbox-container'></div>

    //               <button class='navigation-button next-btn' onclick='event.preventDefault();changeImage(1, ""{lightboxId}"", ""{counterId}"", ""{containerId}"", ""{dotsId}"", {images.Count})'>
    //                   <svg width='40' height='40' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
    //                       <path d='M9 18L15 12L9 6' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/>
    //                   </svg>
    //               </button>

    //               <!-- 圆点指示器 -->
    //               <div id='{dotsId}' class='lightbox-dots'></div>

    //               <button class='lightbox-close' onclick='event.preventDefault();closeLightbox(""{lightboxId}"")'>
    //                   <svg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
    //                       <path d='M18 6L6 18M6 6L18 18' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/>
    //                   </svg>
    //               </button>
    //           </div>
    //       </div>
    //   ");

    //	// CSS样式 - 移除外部图标依赖
    //	html.Append(@"
    //       <style>
    //           .image-grid-container {
    //               margin: 20px 0;
    //               position: relative;
    //           }

    //           .image-group {
    //               display: flex;
    //               flex-wrap: wrap;
    //               gap: 15px;
    //               margin-bottom: 20px;
    //           }

    //           .image-group.hidden {
    //               display: none;
    //           }

    //           .preview-item {
    //               flex: 0 0 calc(33.333% - 10px);
    //               max-width: calc(33.333% - 10px);
    //               height: 100px;
    //               border-radius: 10px;
    //               overflow: hidden;
    //               cursor: pointer;
    //               box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    //               transition: all 0.3s;
    //               background: linear-gradient(145deg, #fff, #f8f9fa);
    //               position: relative;
    //           }

    //           .preview-item:after {
    //               content: '';
    //               position: absolute;
    //               top: 0;
    //               left: 0;
    //               right: 0;
    //               bottom: 0;
    //               border-radius: 10px;
    //               box-shadow: inset 0 0 0 2px rgba(0,0,0,0.05);
    //               pointer-events: none;
    //           }

    //           .preview-item:hover {
    //               transform: translateY(-5px);
    //               box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    //           }

    //           .preview-item img {
    //               width: 100%;
    //               height: 100%;
    //               object-fit: cover;
    //               transition: transform 0.5s;
    //           }

    //           .preview-item:hover img {
    //               transform: scale(1.05);
    //           }

    //           .toggle-groups {
    //               display: block;
    //               margin: 15px auto;
    //               padding: 8px 20px;
    //               background: #f0f2f5;
    //               border: 1px solid #d9d9d9;
    //               border-radius: 20px;
    //               cursor: pointer;
    //               font-size: 12px;
    //               transition: all 0.3s;
    //               color: #333;
    //               font-weight: 500;
    //           }

    //           .toggle-groups:hover {
    //               background: #e6f7ff;
    //               border-color: #40a9ff;
    //               transform: translateY(-2px);
    //               box-shadow: 0 4px 8px rgba(64, 169, 255, 0.2);
    //               color: #096dd9;
    //           }

    //           /* 灯箱样式 */
    //           .lightbox {
    //               position: fixed;
    //               top: 0;
    //               left: 0;
    //               width: 100%;
    //               height: 100%;
    //               background: rgba(0,0,0,0.95);
    //               display: none;
    //               justify-content: center;
    //               align-items: center;
    //               z-index: 1000;
    //           }

    //           .lightbox-content {
    //               position: relative;
    //               width: 90%;
    //               max-width: 900px;
    //               height: 80vh;
    //               display: flex;
    //               flex-direction: column;
    //               align-items: center;
    //           }

    //           .lightbox-container {
    //               width: 100%;
    //               height: 100%;
    //               position: relative;
    //               overflow: hidden;
    //               border-radius: 12px;
    //               background: #000;
    //               box-shadow: 0 25px 50px -12px rgba(0,0,0,0.5);
    //           }

    //           .lightbox-slide {
    //               position: absolute;
    //               top: 0;
    //               left: 0;
    //               width: 100%;
    //               height: 100%;
    //               opacity: 0;
    //               transition: opacity 0.4s;
    //               display: flex;
    //               justify-content: center;
    //               align-items: center;
    //           }

    //           .lightbox-slide.active {
    //               opacity: 1;
    //           }

    //           .lightbox-slide img {
    //               max-width: 100%;
    //               max-height: 100%;
    //               object-fit: contain;
    //           }

    //           .lightbox-counter {
    //               position: absolute;
    //               top: 20px;
    //               left: 20px;
    //               color: white;
    //               background: rgba(0,0,0,0.5);
    //               padding: 8px 15px;
    //               border-radius: 20px;
    //               font-size: 16px;
    //               z-index: 10;
    //               font-weight: 500;
    //               letter-spacing: 0.5px;
    //           }

    //           /* 自定义导航按钮样式 */
    //           .navigation-button {
    //               position: absolute;
    //               top: 50%;
    //               transform: translateY(-50%);
    //               background: rgba(255,255,255,0.15);
    //               border: none;
    //               width: 60px;
    //               height: 60px;
    //               border-radius: 50%;
    //               display: flex;
    //               justify-content: center;
    //               align-items: center;
    //               cursor: pointer;
    //               transition: all 0.3s;
    //               z-index: 11;
    //               backdrop-filter: blur(4px);
    //               -webkit-backdrop-filter: blur(4px);
    //           }

    //           .navigation-button svg {
    //               transition: transform 0.2s;
    //           }

    //           .navigation-button:hover {
    //               background: rgba(255,255,255,0.3);
    //               transform: translateY(-50%) scale(1.1);
    //           }

    //           .navigation-button:hover svg {
    //               transform: scale(1.2);
    //           }

    //           .prev-btn {
    //               left: 20px;
    //           }

    //           .next-btn {
    //               right: 20px;
    //           }

    //           /* 圆点指示器 */
    //           .lightbox-dots {
    //               position: absolute;
    //               bottom: 20px;
    //               left: 0;
    //               right: 0;
    //               display: flex;
    //               justify-content: center;
    //               gap: 10px;
    //               z-index: 10;
    //           }

    //           .dot {
    //               width: 12px;
    //               height: 12px;
    //               border-radius: 50%;
    //               background: rgba(255,255,255,0.4);
    //               cursor: pointer;
    //               transition: all 0.3s;
    //           }

    //           .dot.active {
    //               background: white;
    //               transform: scale(1.2);
    //               box-shadow: 0 0 8px rgba(255,255,255,0.8);
    //           }

    //           .lightbox-close {
    //               position: absolute;
    //               top: 20px;
    //               right: 20px;
    //               background: rgba(0,0,0,0.5);
    //               border: none;
    //               border-radius: 50%;
    //               width: 45px;
    //               height: 45px;
    //               cursor: pointer;
    //               z-index: 11;
    //               display: flex;
    //               justify-content: center;
    //               align-items: center;
    //               transition: all 0.3s;
    //           }

    //           .lightbox-close:hover {
    //               background: rgba(255,255,255,0.2);
    //               transform: scale(1.1);
    //           }

    //           .lightbox-close svg {
    //               width: 20px;
    //               height: 20px;
    //               stroke: white;
    //           }

    //           .no-images {
    //               text-align: center;
    //               padding: 20px;
    //               color: #666;
    //               font-size: 16px;
    //           }

    //           /* 响应式调整 */
    //           @media (max-width: 768px) {
    //               .preview-item {
    //                   flex: 0 0 calc(50% - 10px);
    //                   max-width: calc(50% - 10px);
    //                   height: 120px;
    //               }

    //               .navigation-button {
    //                   width: 40px;
    //                   height: 40px;
    //               }
    //           }

    //           @media (max-width: 480px) {
    //               .preview-item {
    //                   flex: 0 0 100%;
    //                   max-width: 100%;
    //                   height: 100px;
    //               }
    //           }
    //       </style>
    //   ");

    //	// JavaScript - 修改分组控制功能
    //	html.Append($@"
    //       <script>
    //           // 图片灯箱状态
    //           let currentIndex = 0;
    //           let totalImages = 0;
    //           let lightboxElem, containerElem, dotsContainer;
    //           let touchStartX = 0;
    //           let touchEndX = 0;

    //           // 打开灯箱
    //           function openLightbox(index, lightboxId, counterId, containerId, dotsId, total) {{
    //               currentIndex = index;
    //               totalImages = total;
    //               lightboxElem = document.getElementById(lightboxId);
    //               containerElem = document.getElementById(containerId);
    //               dotsContainer = document.getElementById(dotsId);

    //               lightboxElem.style.display = 'flex';
    //               updateLightboxImage();
    //               createDots();

    //               // 更新计数器
    //               document.getElementById(counterId).textContent = `${{index + 1}}/${{total}}`;

    //               // 添加触摸事件
    //               containerElem.addEventListener('touchstart', handleTouchStart);
    //               containerElem.addEventListener('touchend', handleTouchEnd);
    //           }}

    //           // 更新灯箱图片
    //           function updateLightboxImage() {{
    //               containerElem.innerHTML = '';

    //               const slide = document.createElement('div');
    //               slide.className = 'lightbox-slide active';

    //               const previewItem = document.querySelectorAll('.preview-item')[currentIndex];
    //               const imgSrc = previewItem.querySelector('img').src;
    //               const imgAlt = previewItem.querySelector('img').alt;

    //               slide.innerHTML = `<img src='${{imgSrc}}' alt='${{imgAlt}}'>`;
    //               containerElem.appendChild(slide);
    //           }}

    //           // 创建小圆点指示器
    //           function createDots() {{
    //               dotsContainer.innerHTML = '';

    //               for (let i = 0; i < totalImages; i++) {{
    //                   const dot = document.createElement('div');
    //                   dot.className = 'dot' + (i === currentIndex ? ' active' : '');
    //                   dot.addEventListener('click', () => navigateToImage(i));
    //                   dotsContainer.appendChild(dot);
    //               }}
    //           }}

    //           // 导航到指定图片
    //           function navigateToImage(index) {{
    //               currentIndex = index;
    //               updateLightboxImage();
    //               updateDots();
    //               lightboxElem.querySelector('.lightbox-counter').textContent = `${{index + 1}}/${{totalImages}}`;
    //           }}

    //           // 更新小圆点状态
    //           function updateDots() {{
    //               const dots = dotsContainer.querySelectorAll('.dot');
    //               dots.forEach((dot, index) => {{
    //                   dot.classList.toggle('active', index === currentIndex);
    //               }});
    //           }}

    //           // 关闭灯箱
    //           function closeLightbox(lightboxId) {{
    //               document.getElementById(lightboxId).style.display = 'none';

    //               // 移除触摸事件
    //               if (containerElem) {{
    //                   containerElem.removeEventListener('touchstart', handleTouchStart);
    //                   containerElem.removeEventListener('touchend', handleTouchEnd);
    //               }}
    //           }}

    //           // 处理触摸开始
    //           function handleTouchStart(e) {{
    //               touchStartX = e.touches[0].clientX;
    //           }}

    //           // 处理触摸结束
    //           function handleTouchEnd(e) {{
    //               touchEndX = e.changedTouches[0].clientX;
    //               handleSwipe();
    //           }}

    //           // 处理滑动操作
    //           function handleSwipe() {{
    //               const diff = touchStartX - touchEndX;
    //               const threshold = 50; // 滑动阈值

    //               if (diff > threshold) {{
    //                   // 向左滑动 - 下一张
    //                   changeImage(1, lightboxElem.id, '', containerElem.id, dotsContainer.id, totalImages);
    //               }} else if (diff < -threshold) {{
    //                   // 向右滑动 - 上一张
    //                   changeImage(-1, lightboxElem.id, '', containerElem.id, dotsContainer.id, totalImages);
    //               }}
    //           }}

    //           // 切换图片
    //           function changeImage(direction, lightboxId, counterId, containerId, dotsId, total) {{
    //               currentIndex = (currentIndex + direction + total) % total;
    //               updateLightboxImage();
    //               updateDots();
    //               lightboxElem.querySelector('.lightbox-counter').textContent = `${{currentIndex + 1}}/${{total}}`;
    //           }}

    //           // 键盘支持
    //           document.addEventListener('keydown', function(e) {{
    //               if (!lightboxElem || lightboxElem.style.display !== 'flex') return;

    //               if (e.key === 'Escape') closeLightbox(lightboxElem.id);
    //               if (e.key === 'ArrowLeft') changeImage(-1, lightboxElem.id, '', containerElem.id, dotsContainer.id, totalImages);
    //               if (e.key === 'ArrowRight') changeImage(1, lightboxElem.id, '', containerElem.id, dotsContainer.id, totalImages);
    //           }});

    //           // 切换图片组展开/收起（作用域限定在当前容器）
    //           function toggleImageGroups(containerId, button, groupCount) {{
    //               const container = document.getElementById(containerId);
    //               const groups = container.querySelectorAll('.image-group[data-group]');
    //               const isExpanded = groups[1] && window.getComputedStyle(groups[1]).display !== 'none';

    //               // 切换所有隐藏组的显示状态
    //               for (let i = 1; i < groups.length; i++) {{
    //                   groups[i].style.display = isExpanded ? 'none' : 'flex';
    //               }}

    //               // 更新按钮文本
    //               button.innerHTML = isExpanded 
    //                   ? `>> 展开更多 (${{groupCount - 1}} 组)` 
    //                   : `<< 收起 (${{groupCount - 1}} 组)`;
    //           }}
    //       </script>
    //   ");

    //	html.Append("</div>");
    //	return html.ToString();
    //}

    /// <summary>
    /// 将图片二进制数据转为 Base64 字符串（用于 <img src="data:...">）
    /// </summary>
    /// <param name="imageData">图片二进制数据</param>
    /// <param name="contentType">内容类型（如 image/png）</param>
    /// <returns>Base64 格式的 Data URI</returns>
    private string ConvertToBase64(byte[] imageData, string contentType)
    {
        return $"data:{contentType};base64,{Convert.ToBase64String(imageData)}";
    }
    //已经处理只用一个重复灯箱  导致渲染多条追加记录图片数据污染问题 灯箱用了（使用独立ID）解决了
    //private string GenerateImageGridHtml(List<FollowRecordImage> images)
    //{
    //	if (images == null || images.Count == 0)
    //		return "<div class='no-images'>无关联图片</div>";

    //	// 创建唯一ID
    //	string lightboxId = "lb_" + Guid.NewGuid().ToString("N");
    //	string imgContainerId = "imgCtn_" + Guid.NewGuid().ToString("N");

    //	// 图片分组（每组最多3张）
    //	var groups = new List<List<FollowRecordImage>>();
    //	for (int i = 0; i < images.Count; i += 3)
    //	{
    //		groups.Add(images.Skip(i).Take(3).ToList());
    //	}

    //	StringBuilder html = new StringBuilder();
    //	html.Append($"<div id='{imgContainerId}' class='image-grid-container'>");

    //	// 图片网格
    //	for (int groupIndex = 0; groupIndex < groups.Count; groupIndex++)
    //	{
    //		var group = groups[groupIndex];
    //		string groupClass = groupIndex == 0 ? "image-group" : "image-group hidden";

    //		html.Append($"<div class='{groupClass}' data-group='{groupIndex}'>");

    //		foreach (var img in group)
    //		{
    //			string base64Src = ConvertToBase64(img.ImageData, img.ContentType);

    //			// 使用自定义数据属性存储图片信息
    //			html.Append($@"
    //               <div class='preview-item' 
    //                   data-src='{base64Src}'
    //                   data-alt='{img.FileName}'
    //                   onclick='openLightbox(""{imgContainerId}"", this)'>
    //                   <img src='{base64Src}' alt='{img.FileName}'>
    //               </div>
    //           ");
    //		}

    //		html.Append("</div>");
    //	}

    //	// 展开/收起按钮
    //	if (groups.Count > 1)
    //	{
    //		html.Append($@"
    //           <button class='toggle-groups' 
    //               onclick='toggleImageView(this, ""{imgContainerId}"", {groups.Count})'>
    //               >> 展开更多 ({groups.Count - 1} 组)
    //           </button>
    //       ");
    //	}

    //	// 灯箱结构（使用独立ID）
    //	html.Append($@"
    //       <div id='{lightboxId}' class='lightbox'>
    //           <div class='lightbox-content'>

    //               <div class='lightbox-counter'></div>
    //               <button class='nav-btn prev-btn' onclick='changeImage(""{lightboxId}"", -1)'>
    //                   <i class='fas fa-chevron-left'></i>
    //               </button>
    //               <div class='lightbox-container'></div>
    //               <button class='nav-btn next-btn' onclick='changeImage(""{lightboxId}"", 1)'>
    //                   <i class='fas fa-chevron-right'></i>
    //               </button>
    //               <div class='lightbox-dots'></div>
    //               <button class='lightbox-close' onclick='closeLightbox(""{lightboxId}"")'>
    //                   <i class='fas fa-times'></i>
    //               </button>
    //           </div>
    //       </div>
    //   ");
    //	// CSS样式（保持不变）
    //	html.Append(@"
    //       <style>
    //           /* 原有CSS保持不变 */
    //           .image-grid-container { margin: 20px 0; }
    //           .image-group { display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px; }
    //           .image-group.hidden { display: none; }
    //           .preview-item { 
    //               flex: 0 0 calc(33.333% - 10px); 
    //               height: 150px; 
    //               border-radius: 10px; 
    //               overflow: hidden; 
    //               cursor: pointer; 
    //               box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    //               transition: all 0.3s;
    //           }
    //           .preview-item:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.2); }
    //           .preview-item img { width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s; }
    //           .preview-item:hover img { transform: scale(1.05); }
    //           .toggle-groups { 
    //               display: block; 
    //               margin: 15px auto; 
    //               padding: 8px 20px; 
    //               background: #f0f2f5; 
    //               border: 1px solid #d9d9d9; 
    //               border-radius: 20px; 
    //               cursor: pointer;
    //               transition: all 0.3s;
    //           }
    //           .toggle-groups:hover { 
    //               background: #e6f7ff; 
    //               border-color: #40a9ff; 
    //               transform: translateY(-2px); 
    //               box-shadow: 0 4px 8px rgba(64, 169, 255, 0.2);
    //           }
    //           .lightbox { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.95); display: none; justify-content: center; align-items: center; z-index: 1000; }
    //           .lightbox-content { position: relative; width: 90%; max-width: 900px; height: 80vh; display: flex; flex-direction: column; align-items: center; }
    //           .lightbox-container { width: 100%; height: 100%; position: relative; overflow: hidden; border-radius: 12px; background: #000; }
    //           .lightbox-slide { position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; transition: opacity 0.4s; display: flex; justify-content: center; align-items: center; }
    //           .lightbox-slide.active { opacity: 1; }
    //           .lightbox-slide img { max-width: 100%; max-height: 100%; object-fit: contain; }
    //           .lightbox-counter { position: absolute; top: 20px; left: 20px; color: white; background: rgba(0,0,0,0.5); padding: 8px 15px; border-radius: 20px; font-size: 16px; z-index: 10; }
    //           .nav-btn { position: absolute; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.2); border: none; width: 60px; height: 60px; border-radius: 50%; display: flex; justify-content: center; align-items: center; cursor: pointer; transition: all 0.3s; z-index: 11; }
    //           .nav-btn i { font-size: 24px; color: white; }
    //           .nav-btn:hover { background: rgba(255,255,255,0.3); transform: translateY(-50%) scale(1.1); }
    //           .prev-btn { left: 20px; }
    //           .next-btn { right: 20px; }
    //           .lightbox-dots { position: absolute; bottom: 20px; left: 0; right: 0; display: flex; justify-content: center; gap: 10px; z-index: 10; }
    //           .dot { width: 12px; height: 12px; border-radius: 50%; background: rgba(255,255,255,0.4); cursor: pointer; transition: all 0.3s; }
    //           .dot.active { background: white; transform: scale(1.2); }
    //           .lightbox-close { position: absolute; top: 20px; right: 20px; font-size: 24px; color: white; background: rgba(0,0,0,0.5); border: none; border-radius: 50%; width: 45px; height: 45px; cursor: pointer; z-index: 11; display: flex; justify-content: center; align-items: center; transition: all 0.3s; }
    //           .lightbox-close i { font-size: 20px; }
    //           .lightbox-close:hover { background: rgba(255,255,255,0.2); transform: scale(1.1); }
    //           @media (max-width: 768px) {
    //               .preview-item { height: 120px; }
    //               .nav-btn { width: 40px; height: 40px; }
    //               .nav-btn i { font-size: 18px; }
    //           }
    //       </style>
    //   ");

    //	// JavaScript - 完全重构状态管理
    //	html.Append($@"
    //       <script>
    //           // 打开灯箱（使用DOM数据而非全局变量）
    //           function openLightbox(containerId, previewItem) {{
    //               const container = document.getElementById(containerId);
    //               const lightbox = container.querySelector('.lightbox');
    //               const previewItems = container.querySelectorAll('.preview-item');

    //               // 获取当前索引
    //               let index = 0;
    //               for (let i = 0; i < previewItems.length; i++) {{
    //                   if (previewItems[i] === previewItem) {{
    //                       index = i;
    //                       break;
    //                   }}
    //               }}

    //               // 存储状态到DOM元素
    //               container.dataset.currentIndex = index;
    //               container.dataset.totalImages = previewItems.length;

    //               // 显示灯箱
    //               lightbox.style.display = 'flex';
    //               updateLightboxImage(containerId);
    //               createDots(containerId);
    //           }}

    //           // 更新灯箱图片
    //           function updateLightboxImage(containerId) {{
    //               const container = document.getElementById(containerId);
    //               const lightboxContainer = container.querySelector('.lightbox-container');
    //               const counter = container.querySelector('.lightbox-counter');

    //               const currentIndex = parseInt(container.dataset.currentIndex || 0);
    //               const totalImages = parseInt(container.dataset.totalImages || 0);
    //               const previewItems = container.querySelectorAll('.preview-item');

    //               // 清除现有内容
    //               lightboxContainer.innerHTML = '';

    //               // 创建新幻灯片
    //               const slide = document.createElement('div');
    //               slide.className = 'lightbox-slide active';

    //               // 直接使用预览项的图片数据
    //               const imgSrc = previewItems[currentIndex].dataset.src;
    //               const imgAlt = previewItems[currentIndex].dataset.alt;

    //               slide.innerHTML = `<img src='${{imgSrc}}' alt='${{imgAlt}}'>`;
    //               lightboxContainer.appendChild(slide);

    //               // 更新计数器
    //               counter.textContent = `${{currentIndex + 1}}/${{totalImages}}`;
    //           }}

    //           // 创建小圆点指示器
    //           function createDots(containerId) {{
    //               const container = document.getElementById(containerId);
    //               const dotsContainer = container.querySelector('.lightbox-dots');
    //               const totalImages = parseInt(container.dataset.totalImages || 0);
    //               const currentIndex = parseInt(container.dataset.currentIndex || 0);

    //               dotsContainer.innerHTML = '';

    //               for (let i = 0; i < totalImages; i++) {{
    //                   const dot = document.createElement('div');
    //                   dot.className = 'dot' + (i === currentIndex ? ' active' : '');
    //                   dot.addEventListener('click', () => navigateToImage(i, containerId));
    //                   dotsContainer.appendChild(dot);
    //               }}
    //           }}

    //           // 导航到指定图片
    //           function navigateToImage(index, containerId) {{
    //               const container = document.getElementById(containerId);
    //               container.dataset.currentIndex = index;
    //               updateLightboxImage(containerId);
    //               updateDots(containerId);
    //           }}

    //           // 更新小圆点状态
    //           function updateDots(containerId) {{
    //               const container = document.getElementById(containerId);
    //               const dotsContainer = container.querySelector('.lightbox-dots');
    //               const dots = dotsContainer.querySelectorAll('.dot');
    //               const currentIndex = parseInt(container.dataset.currentIndex || 0);

    //               dots.forEach((dot, index) => {{
    //                   dot.classList.toggle('active', index === currentIndex);
    //               }});
    //           }}

    //           // 关闭灯箱
    //           function closeLightbox(lightboxId) {{
    //               const lightbox = document.getElementById(lightboxId);
    //               lightbox.style.display = 'none';
    //           }}

    //           // 切换图片
    //           function changeImage(lightboxId, direction) {{
    //               const lightbox = document.getElementById(lightboxId);
    //               const container = lightbox.closest('.image-grid-container');
    //               const currentIndex = parseInt(container.dataset.currentIndex || 0);
    //               const totalImages = parseInt(container.dataset.totalImages || 0);

    //               let newIndex = currentIndex + direction;
    //               if (newIndex < 0) newIndex = totalImages - 1;
    //               if (newIndex >= totalImages) newIndex = 0;

    //               container.dataset.currentIndex = newIndex;
    //               updateLightboxImage(container.id);
    //               updateDots(container.id);
    //           }}

    //           // 键盘支持（全局事件，但通过DOM元素获取状态）
    //           document.addEventListener('keydown', function(e) {{
    //               const activeLightbox = document.querySelector('.lightbox[style=""display: flex;""]');
    //               if (!activeLightbox) return;

    //               const container = activeLightbox.closest('.image-grid-container');

    //               if (e.key === 'Escape') closeLightbox(activeLightbox.id);
    //               if (e.key === 'ArrowLeft') changeImage(activeLightbox.id, -1);
    //               if (e.key === 'ArrowRight') changeImage(activeLightbox.id, 1);
    //           }});

    //           // 简化展开/收起功能
    //           function toggleImageView(button, containerId, groupCount) {{
    //               event.preventDefault();
    //               const container = document.getElementById(containerId);
    //               const groups = container.querySelectorAll('.image-group');
    //               const isExpanded = groups[1] && !groups[1].classList.contains('hidden');

    //               for (let i = 1; i < groups.length; i++) {{
    //                   groups[i].classList.toggle('hidden', isExpanded);
    //               }}

    //               button.innerHTML = isExpanded 
    //                   ? `>> 展开更多 (${{groupCount - 1}} 组)` 
    //                   : `<< 收起 (${{groupCount - 1}} 组)`;
    //           }}
    //       </script>
    //   ");
    //	html.Append("</div>");
    //	return html.ToString();
    //}
 //   private string GenerateImageGridHtml(List<FollowRecordImage> images)
	//{
	//	if (images == null || images.Count == 0)
	//		return "<div class='no-images'>无关联图片</div>";

	//	// 创建唯一ID
	//	string lightboxId = "lb_" + Guid.NewGuid().ToString("N");
	//	string imgContainerId = "imgCtn_" + Guid.NewGuid().ToString("N");

	//	// 图片分组（每组最多3张）
	//	var groups = new List<List<FollowRecordImage>>();
	//	for (int i = 0; i < images.Count; i += 3)
	//	{
	//		groups.Add(images.Skip(i).Take(3).ToList());
	//	}

	//	StringBuilder html = new StringBuilder();
	//	html.Append($"<div id='{imgContainerId}' class='image-grid-container'>");

	//	// 图片网格
	//	for (int groupIndex = 0; groupIndex < groups.Count; groupIndex++)
	//	{
	//		var group = groups[groupIndex];
	//		string groupClass = groupIndex == 0 ? "image-group" : "image-group hidden";

	//		html.Append($"<div class='{groupClass}' data-group='{groupIndex}'>");

	//		foreach (var img in group)
	//		{
	//			string base64Src = ConvertToBase64(img.ImageData, img.ContentType);

	//			// 使用自定义数据属性存储图片信息
	//			html.Append($@"
 //               <div class='preview-item' 
 //                   data-src='{base64Src}'
 //                   data-alt='{img.FileName}'
 //                   onclick='openLightbox(""{imgContainerId}"", this)'>
 //                   <img src='{base64Src}' alt='{img.FileName}'>
 //               </div>
 //           ");
	//		}

	//		html.Append("</div>");
	//	}

	//	// 展开/收起按钮
	//	if (groups.Count > 1)
	//	{
	//		html.Append($@"
 //           <button class='toggle-groups' 
 //               onclick='toggleImageView(this, ""{imgContainerId}"", {groups.Count})'>
 //               >> 展开更多 ({groups.Count - 1} 组)
 //           </button>
 //       ");
	//	}

	//	// 灯箱结构（使用独立ID）
	//	html.Append($@"
 //       <div id='{lightboxId}' class='lightbox'>
 //           <div class='lightbox-content'>
 //               <button class='lightbox-close' onclick='event.preventDefault();closeLightbox(""{lightboxId}"")'>
 //                   <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'>
 //                       <path fill='white' d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/>
 //                   </svg>
 //               </button>
 //               <div class='lightbox-counter'></div>
 //               <button class='nav-btn prev-btn' onclick='event.preventDefault();changeImage(""{lightboxId}"", -1)'>
 //                   <i class='fas fa-chevron-left'></i>
 //               </button>
 //               <div class='lightbox-container'></div>
 //               <button class='nav-btn next-btn' onclick='event.preventDefault();changeImage(""{lightboxId}"", 1)'>
 //                   <i class='fas fa-chevron-right'></i>
 //               </button>
 //               <div class='lightbox-dots'></div>
 //               <button class='lightbox-close' onclick='event.preventDefault();closeLightbox(""{lightboxId}"")'>
 //                   <i class='fas fa-times'></i>
 //               </button>
 //           </div>
 //       </div>
 //   ");
	//	// CSS样式（保持不变）
	//	html.Append(@"
 //       <style>
 //           /* 原有CSS保持不变 */
 //           .image-grid-container { margin: 20px 0; }
 //           .image-group { display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px; }
 //           .image-group.hidden { display: none; }
 //           .preview-item { 
 //               flex: 0 0 calc(33.333% - 10px); 
 //               height: 150px; 
 //               border-radius: 10px; 
 //               overflow: hidden; 
 //               cursor: pointer; 
 //               box-shadow: 0 5px 15px rgba(0,0,0,0.1);
 //               transition: all 0.3s;
 //           }
 //           .preview-item:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.2); }
 //           .preview-item img { width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s; }
 //           .preview-item:hover img { transform: scale(1.05); }
 //           .toggle-groups { 
 //               display: block; 
 //               margin: 15px auto; 
 //               padding: 8px 20px; 
 //               background: #f0f2f5; 
 //               border: 1px solid #d9d9d9; 
 //               border-radius: 20px; 
 //               cursor: pointer;
 //               transition: all 0.3s;
 //           }
 //           .toggle-groups:hover { 
 //               background: #e6f7ff; 
 //               border-color: #40a9ff; 
 //               transform: translateY(-2px); 
 //               box-shadow: 0 4px 8px rgba(64, 169, 255, 0.2);
 //           }
 //           .lightbox { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.95); display: none; justify-content: center; align-items: center; z-index: 1000; }
 //           .lightbox-content { position: relative; width: 90%; max-width: 900px; height: 80vh; display: flex; flex-direction: column; align-items: center; }
 //           .lightbox-container { width: 100%; height: 100%; position: relative; overflow: hidden; border-radius: 12px; background: #000; }
 //           .lightbox-slide { position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; transition: opacity 0.4s; display: flex; justify-content: center; align-items: center; }
 //           .lightbox-slide.active { opacity: 1; }
 //           .lightbox-slide img { max-width: 100%; max-height: 100%; object-fit: contain; }
 //           .lightbox-counter { position: absolute; top: 20px; left: 20px; color: white; background: rgba(0,0,0,0.5); padding: 8px 15px; border-radius: 20px; font-size: 16px; z-index: 10; }
 //           .nav-btn { position: absolute; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.2); border: none; width: 60px; height: 60px; border-radius: 50%; display: flex; justify-content: center; align-items: center; cursor: pointer; transition: all 0.3s; z-index: 11; }
 //           .nav-btn i { font-size: 24px; color: white; }
 //           .nav-btn:hover { background: rgba(255,255,255,0.3); transform: translateY(-50%) scale(1.1); }
 //           .prev-btn { left: 20px; }
 //           .next-btn { right: 20px; }
 //           .lightbox-dots { position: absolute; bottom: 20px; left: 0; right: 0; display: flex; justify-content: center; gap: 10px; z-index: 10; }
 //           .dot { width: 12px; height: 12px; border-radius: 50%; background: rgba(255,255,255,0.4); cursor: pointer; transition: all 0.3s; }
 //           .dot.active { background: white; transform: scale(1.2); }
 //           .lightbox-close { position: absolute; top: 20px; right: 20px; font-size: 24px; color: white; background: rgba(0,0,0,0.5); border: none; border-radius: 50%; width: 45px; height: 45px; cursor: pointer; z-index: 11; display: flex; justify-content: center; align-items: center; transition: all 0.3s; }
 //           //.lightbox-close i { font-size: 20px; }
 //           //.lightbox-close:hover { background: rgba(255,255,255,0.2); transform: scale(1.1); }
 //           /* 新增关闭按钮样式 */
 //           .lightbox-close {position: absolute;top: 25px;right: 25px;background: rgba(0, 0, 0, 0.5);border: none;border-radius: 50%;width: 45px;height: 45px;cursor: pointer;z-index: 1001;display: flex;justify-content: center;align-items: center;transition: all 0.3s;}
 //           .lightbox-close:hover {background: rgba(255, 255, 255, 0.2);transform: scale(1.1);}
 //           .lightbox-close svg {width: 24px;height: 24px;}
 //           @media (max-width: 768px) {
 //               .preview-item { height: 120px; }
 //               .nav-btn { width: 40px; height: 40px; }
 //               .nav-btn i { font-size: 18px; }
 //           }
 //       </style>
 //   ");

	//	// JavaScript - 完全重构状态管理
	//	html.Append($@"
 //       <script>
 //           // 打开灯箱（使用DOM数据而非全局变量）
 //           function openLightbox(containerId, previewItem) {{
 //               const container = document.getElementById(containerId);
 //               const lightbox = container.querySelector('.lightbox');
 //               const previewItems = container.querySelectorAll('.preview-item');

 //               // 获取当前索引
 //               let index = 0;
 //               for (let i = 0; i < previewItems.length; i++) {{
 //                   if (previewItems[i] === previewItem) {{
 //                       index = i;
 //                       break;
 //                   }}
 //               }}

 //               // 存储状态到DOM元素
 //               container.dataset.currentIndex = index;
 //               container.dataset.totalImages = previewItems.length;

 //               // 显示灯箱
 //               lightbox.style.display = 'flex';
 //               updateLightboxImage(containerId);
 //               createDots(containerId);
 //           }}

 //           // 更新灯箱图片
 //           function updateLightboxImage(containerId) {{
 //               const container = document.getElementById(containerId);
 //               const lightboxContainer = container.querySelector('.lightbox-container');
 //               const counter = container.querySelector('.lightbox-counter');

 //               const currentIndex = parseInt(container.dataset.currentIndex || 0);
 //               const totalImages = parseInt(container.dataset.totalImages || 0);
 //               const previewItems = container.querySelectorAll('.preview-item');

 //               // 清除现有内容
 //               lightboxContainer.innerHTML = '';

 //               // 创建新幻灯片
 //               const slide = document.createElement('div');
 //               slide.className = 'lightbox-slide active';

 //               // 直接使用预览项的图片数据
 //               const imgSrc = previewItems[currentIndex].dataset.src;
 //               const imgAlt = previewItems[currentIndex].dataset.alt;

 //               slide.innerHTML = `<img src='${{imgSrc}}' alt='${{imgAlt}}'>`;
 //               lightboxContainer.appendChild(slide);

 //               // 更新计数器
 //               counter.textContent = `${{currentIndex + 1}}/${{totalImages}}`;
 //           }}

 //           // 创建小圆点指示器
 //           function createDots(containerId) {{
 //               const container = document.getElementById(containerId);
 //               const dotsContainer = container.querySelector('.lightbox-dots');
 //               const totalImages = parseInt(container.dataset.totalImages || 0);
 //               const currentIndex = parseInt(container.dataset.currentIndex || 0);

 //               dotsContainer.innerHTML = '';

 //               for (let i = 0; i < totalImages; i++) {{
 //                   const dot = document.createElement('div');
 //                   dot.className = 'dot' + (i === currentIndex ? ' active' : '');
 //                   dot.addEventListener('click', () => navigateToImage(i, containerId));
 //                   dotsContainer.appendChild(dot);
 //               }}
 //           }}

 //           // 导航到指定图片
 //           function navigateToImage(index, containerId) {{
 //               const container = document.getElementById(containerId);
 //               container.dataset.currentIndex = index;
 //               updateLightboxImage(containerId);
 //               updateDots(containerId);
 //           }}

 //           // 更新小圆点状态
 //           function updateDots(containerId) {{
 //               const container = document.getElementById(containerId);
 //               const dotsContainer = container.querySelector('.lightbox-dots');
 //               const dots = dotsContainer.querySelectorAll('.dot');
 //               const currentIndex = parseInt(container.dataset.currentIndex || 0);

 //               dots.forEach((dot, index) => {{
 //                   dot.classList.toggle('active', index === currentIndex);
 //               }});
 //           }}

 //           // 关闭灯箱
 //           //function closeLightbox(lightboxId) {{
 //           //    const lightbox = document.getElementById(lightboxId);
 //           //    lightbox.style.display = 'none';
 //           //}}
 //           // 关闭灯箱
 //           function closeLightbox(lightboxId) {{
 //               const lightbox = document.getElementById(lightboxId);
 //               lightbox.style.display = 'none';
                
 //               // 移除键盘事件监听
 //               //document.removeEventListener('keydown', handleKeyDown);
 //           }}

 //           // 切换图片
 //           function changeImage(lightboxId, direction) {{
 //               const lightbox = document.getElementById(lightboxId);
 //               const container = lightbox.closest('.image-grid-container');
 //               const currentIndex = parseInt(container.dataset.currentIndex || 0);
 //               const totalImages = parseInt(container.dataset.totalImages || 0);

 //               let newIndex = currentIndex + direction;
 //               if (newIndex < 0) newIndex = totalImages - 1;
 //               if (newIndex >= totalImages) newIndex = 0;

 //               container.dataset.currentIndex = newIndex;
 //               updateLightboxImage(container.id);
 //               updateDots(container.id);
 //           }}

 //           // 键盘支持（全局事件，但通过DOM元素获取状态）
 //           document.addEventListener('keydown', function(e) {{
 //               const activeLightbox = document.querySelector('.lightbox[style=""display: flex;""]');
 //               if (!activeLightbox) return;

 //               const container = activeLightbox.closest('.image-grid-container');

 //               if (e.key === 'Escape') closeLightbox(activeLightbox.id);
 //               if (e.key === 'ArrowLeft') changeImage(activeLightbox.id, -1);
 //               if (e.key === 'ArrowRight') changeImage(activeLightbox.id, 1);
 //           }});

 //           // 简化展开/收起功能
 //           function toggleImageView(button, containerId, groupCount) {{
 //               event.preventDefault();
 //               const container = document.getElementById(containerId);
 //               const groups = container.querySelectorAll('.image-group');
 //               const isExpanded = groups[1] && !groups[1].classList.contains('hidden');

 //               for (let i = 1; i < groups.length; i++) {{
 //                   groups[i].classList.toggle('hidden', isExpanded);
 //               }}

 //               button.innerHTML = isExpanded 
 //                   ? `>> 展开更多 (${{groupCount - 1}} 组)` 
 //                   : `<< 收起 (${{groupCount - 1}} 组)`;
 //           }}
 //       </script>
 //   ");
	//	html.Append("</div>");
	//	return html.ToString();
	//}
    //增加了手机端触摸滑动功能

    private string GenerateImageGridHtml(List<FollowRecordImage> images)
    {
        if (images == null || images.Count == 0)
            return "<div class='no-images'>无关联图片</div>";

        // 创建唯一ID
        string lightboxId = "lb_" + Guid.NewGuid().ToString("N");
        string imgContainerId = "imgCtn_" + Guid.NewGuid().ToString("N");

        // 图片分组（每组最多3张）
        var groups = new List<List<FollowRecordImage>>();
        for (int i = 0; i < images.Count; i += 3)
        {
            groups.Add(images.Skip(i).Take(3).ToList());
        }

        StringBuilder html = new StringBuilder();
        html.Append($"<div id='{imgContainerId}' class='image-grid-container'>");

        // 图片网格
        for (int groupIndex = 0; groupIndex < groups.Count; groupIndex++)
        {
            var group = groups[groupIndex];
            string groupClass = groupIndex == 0 ? "image-group" : "image-group hidden";

            html.Append($"<div class='{groupClass}' data-group='{groupIndex}'>");

            foreach (var img in group)
            {
                string base64Src = ConvertToBase64(img.ImageData, img.ContentType);

                // 使用自定义数据属性存储图片信息
                html.Append($@"
                <div class='preview-item' 
                    data-src='{base64Src}'
                    data-alt='{img.FileName}'
                    onclick='openLightbox(""{imgContainerId}"", this)'>
                    <img src='{base64Src}' alt='{img.FileName}'>
                </div>
            ");
            }

            html.Append("</div>");
        }

        // 展开/收起按钮
        if (groups.Count > 1)
        {
            html.Append($@"
            <button class='toggle-groups' 
                onclick='toggleImageView(this, ""{imgContainerId}"", {groups.Count})'>
                >> 展开更多 ({groups.Count - 1} 组)
            </button>
        ");
        }

        // 灯箱结构（使用独立ID）
        html.Append($@"
        <div id='{lightboxId}' class='lightbox'>
            <div class='lightbox-content'>
                <button class='lightbox-close' onclick='event.preventDefault();closeLightbox(""{lightboxId}"")'>
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'>
                        <path fill='white' d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/>
                    </svg>
                </button>
                <div class='lightbox-counter'></div>
                <button class='nav-btn prev-btn' onclick='event.preventDefault();changeImage(""{lightboxId}"", -1)'>
                    <i class='fas fa-chevron-left'></i>
                </button>
                <div class='lightbox-container'></div>
                <button class='nav-btn next-btn' onclick='event.preventDefault();changeImage(""{lightboxId}"", 1)'>
                    <i class='fas fa-chevron-right'></i>
                </button>
                <div class='lightbox-dots'></div>
            </div>
        </div>
    ");

        // CSS样式 - 添加触摸滑动支持
        html.Append(@"
        <style>
            /* 原有CSS保持不变 */
            .image-grid-container { margin: 20px 0; }
            .image-group { display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px; }
            .image-group.hidden { display: none; }
            .preview-item { 
                flex: 0 0 calc(33.333% - 10px); 
                height: 150px; 
                border-radius: 10px; 
                overflow: hidden; 
                cursor: pointer; 
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                transition: all 0.3s;
            }
            .preview-item:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.2); }
            .preview-item img { width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s; }
            .preview-item:hover img { transform: scale(1.05); }
            .toggle-groups { 
                display: block; 
                margin: 15px auto; 
                padding: 8px 20px; 
                background: #f0f2f5; 
                border: 1px solid #d9d9d9; 
                border-radius: 20px; 
                cursor: pointer;
                transition: all 0.3s;
            }
            .toggle-groups:hover { 
                background: #e6f7ff; 
                border-color: #40a9ff; 
                transform: translateY(-2px); 
                box-shadow: 0 4px 8px rgba(64, 169, 255, 0.2);
            }
            .lightbox { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.95); display: none; justify-content: center; align-items: center; z-index: 1000; }
            .lightbox-content { position: relative; width: 90%; max-width: 900px; height: 80vh; display: flex; flex-direction: column; align-items: center; }
            .lightbox-container { width: 100%; height: 100%; position: relative; overflow: hidden; border-radius: 12px; background: #000; }
            .lightbox-slide { position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; transition: opacity 0.4s; display: flex; justify-content: center; align-items: center; }
            .lightbox-slide.active { opacity: 1; }
            .lightbox-slide img { max-width: 100%; max-height: 100%; object-fit: contain; }
            .lightbox-counter { position: absolute; top: 20px; left: 20px; color: white; background: rgba(0,0,0,0.5); padding: 8px 15px; border-radius: 20px; font-size: 16px; z-index: 10; }
            .nav-btn { position: absolute; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.2); border: none; width: 60px; height: 60px; border-radius: 50%; display: flex; justify-content: center; align-items: center; cursor: pointer; transition: all 0.3s; z-index: 11; }
            .nav-btn i { font-size: 24px; color: white; }
            .nav-btn:hover { background: rgba(255,255,255,0.3); transform: translateY(-50%) scale(1.1); }
            .prev-btn { left: 20px; }
            .next-btn { right: 20px; }
            .lightbox-dots { position: absolute; bottom: 20px; left: 0; right: 0; display: flex; justify-content: center; gap: 10px; z-index: 10; }
            .dot { width: 12px; height: 12px; border-radius: 50%; background: rgba(255,255,255,0.4); cursor: pointer; transition: all 0.3s; }
            .dot.active { background: white; transform: scale(1.2); }
            .lightbox-close { position: absolute; top: 20px; right: 20px; font-size: 24px; color: white; background: rgba(0,0,0,0.5); border: none; border-radius: 50%; width: 45px; height: 45px; cursor: pointer; z-index: 11; display: flex; justify-content: center; align-items: center; transition: all 0.3s; }
            .lightbox-close:hover { background: rgba(255,255,255,0.2); transform: scale(1.1); }
            .lightbox-close svg { width: 24px; height: 24px; }
            
            /* 添加滑动过渡效果 */
            //.lightbox-slide {
            //    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
            //}
            //.lightbox-slide.slide-left {
            //    transform: translateX(-100%);
            //    opacity: 0;
            //}
            //.lightbox-slide.slide-right {
            //    transform: translateX(100%);
            //    opacity: 0;
            //}
            
            .lightbox-slide {
                transition: transform 0.45s cubic-bezier(0.22, 0.61, 0.36, 1), 
                            opacity 0.45s cubic-bezier(0.22, 0.61, 0.36, 1);
                will-change: transform, opacity;
                backface-visibility: hidden;
            }

            .lightbox-slide.slide-left {
                transform: translate3d(-100%, 0, 0);
                opacity: 0;
            }

            .lightbox-slide.slide-right {
                transform: translate3d(100%, 0, 0);
                opacity: 0;
            }
            
            @media (max-width: 768px) {
                .preview-item { height: 120px; }
                .nav-btn { width: 40px; height: 40px; }
                .nav-btn i { font-size: 18px; }
            }
        </style>
    ");

        // JavaScript - 添加触屏滑动支持
        html.Append($@"
        <script>
            // 触摸事件处理变量
            let touchStartX = 0;
            let touchEndX = 0;
            let touchStartY = 0;
            let touchEndY = 0;
            const swipeThreshold = 50; // 滑动阈值（像素）
            
            // 打开灯箱
            function openLightbox(containerId, previewItem) {{
                const container = document.getElementById(containerId);
                const lightbox = container.querySelector('.lightbox');
                const previewItems = container.querySelectorAll('.preview-item');

                // 获取当前索引
                let index = 0;
                for (let i = 0; i < previewItems.length; i++) {{
                    if (previewItems[i] === previewItem) {{
                        index = i;
                        break;
                    }}
                }}

                // 存储状态到DOM元素
                container.dataset.currentIndex = index;
                container.dataset.totalImages = previewItems.length;

                // 显示灯箱
                lightbox.style.display = 'flex';
                updateLightboxImage(containerId);
                createDots(containerId);
                
                // 添加触摸事件监听
                const lightboxContainer = container.querySelector('.lightbox-container');
                lightboxContainer.addEventListener('touchstart', handleTouchStart, {{ passive: true }});
                lightboxContainer.addEventListener('touchmove', handleTouchMove, {{ passive: false }});
                lightboxContainer.addEventListener('touchend', handleTouchEnd);
            }}

            // 关闭灯箱
            function closeLightbox(lightboxId) {{
                const lightbox = document.getElementById(lightboxId);
                const container = lightbox.closest('.image-grid-container');
                const lightboxContainer = container.querySelector('.lightbox-container');
                
                // 移除触摸事件监听
                lightboxContainer.removeEventListener('touchstart', handleTouchStart);
                lightboxContainer.removeEventListener('touchmove', handleTouchMove);
                lightboxContainer.removeEventListener('touchend', handleTouchEnd);

                lightbox.style.display = 'none';
            }}
            
            // 触摸开始事件
            function handleTouchStart(event) {{
                // 记录触摸起始位置
                touchStartX = event.changedTouches[0].screenX;
                touchStartY = event.changedTouches[0].screenY;
            }}
            
            // 触摸移动事件
            function handleTouchMove(event) {{
                // 阻止页面滚动
                if (Math.abs(event.changedTouches[0].screenX - touchStartX) > 10) {{
                    event.preventDefault();
                }}
            }}
            
            // 触摸结束事件
            function handleTouchEnd(event) {{
                // 记录触摸结束位置
                touchEndX = event.changedTouches[0].screenX;
                touchEndY = event.changedTouches[0].screenY;
                
                // 计算滑动距离
                const diffX = touchEndX - touchStartX;
                const diffY = touchEndY - touchStartY;
                
                // 忽略垂直滑动
                if (Math.abs(diffY) > Math.abs(diffX)) return;
                
                // 获取当前激活的灯箱
                const activeLightbox = document.querySelector('.lightbox[style=""display: flex;""]');
                if (!activeLightbox) return;
                
                // 向左滑动（下一张）
                if (diffX < -swipeThreshold) {{
                    changeImage(activeLightbox.id, 1);
                }} 
                // 向右滑动（上一张）
                else if (diffX > swipeThreshold) {{
                    changeImage(activeLightbox.id, -1);
                }}
            }}
            
            // 平滑切换图片（带滑动动画）
            function smoothChangeImage(lightboxId, direction) {{
                const lightbox = document.getElementById(lightboxId);
                const container = lightbox.closest('.image-grid-container');
                const currentIndex = parseInt(container.dataset.currentIndex || 0);
                const totalImages = parseInt(container.dataset.totalImages || 0);
                const lightboxContainer = container.querySelector('.lightbox-container');
                
                // 计算新索引
                let newIndex = currentIndex + direction;
                if (newIndex < 0) newIndex = totalImages - 1;
                if (newIndex >= totalImages) newIndex = 0;
                
                // 获取当前显示的图片
                const currentSlide = lightboxContainer.querySelector('.lightbox-slide');
                
                // 创建新幻灯片
                const newSlide = document.createElement('div');
                newSlide.className = 'lightbox-slide';
                
                // 获取图片数据
                const previewItems = container.querySelectorAll('.preview-item');
                const imgSrc = previewItems[newIndex].dataset.src;
                const imgAlt = previewItems[newIndex].dataset.alt;
                
                newSlide.innerHTML = `<img src='${{imgSrc}}' alt='${{imgAlt}}'>`;
                lightboxContainer.appendChild(newSlide);
                
                // 添加滑动方向类
                if (direction > 0) {{
                    newSlide.classList.add('slide-left');
                }} else {{
                    newSlide.classList.add('slide-right');
                }}
                
                // 触发重绘
                void newSlide.offsetWidth;
                
                // 应用动画
                if (direction > 0) {{
                    currentSlide.classList.add('slide-left');
                    newSlide.classList.remove('slide-left');
                }} else {{
                    currentSlide.classList.add('slide-right');
                    newSlide.classList.remove('slide-right');
                }}
                
                // 设置新索引
                container.dataset.currentIndex = newIndex;
                
                // 更新计数器和小圆点
                updateCounter(container);
                updateDots(container.id);
                
                // 动画结束后清理
                setTimeout(() => {{
                    lightboxContainer.innerHTML = '';
                    newSlide.classList.add('active');
                    lightboxContainer.appendChild(newSlide);
                }}, 300);
            }}
            
            // 更新计数器
            function updateCounter(container) {{
                const counter = container.querySelector('.lightbox-counter');
                const currentIndex = parseInt(container.dataset.currentIndex || 0);
                const totalImages = parseInt(container.dataset.totalImages || 0);
                counter.textContent = `${{currentIndex + 1}}/${{totalImages}}`;
            }}

            // 更新灯箱图片
            function updateLightboxImage(containerId) {{
                const container = document.getElementById(containerId);
                const lightboxContainer = container.querySelector('.lightbox-container');
                const counter = container.querySelector('.lightbox-counter');
                const currentIndex = parseInt(container.dataset.currentIndex || 0);
                const totalImages = parseInt(container.dataset.totalImages || 0);
                const previewItems = container.querySelectorAll('.preview-item');

                // 清除现有内容
                lightboxContainer.innerHTML = '';

                // 创建新幻灯片
                const slide = document.createElement('div');
                slide.className = 'lightbox-slide active';

                // 直接使用预览项的图片数据
                const imgSrc = previewItems[currentIndex].dataset.src;
                const imgAlt = previewItems[currentIndex].dataset.alt;

                slide.innerHTML = `<img src='${{imgSrc}}' alt='${{imgAlt}}'>`;
                lightboxContainer.appendChild(slide);

                // 更新计数器
                counter.textContent = `${{currentIndex + 1}}/${{totalImages}}`;
            }}

            // 创建小圆点指示器
            function createDots(containerId) {{
                const container = document.getElementById(containerId);
                const dotsContainer = container.querySelector('.lightbox-dots');
                const totalImages = parseInt(container.dataset.totalImages || 0);
                const currentIndex = parseInt(container.dataset.currentIndex || 0);

                dotsContainer.innerHTML = '';

                for (let i = 0; i < totalImages; i++) {{
                    const dot = document.createElement('div');
                    dot.className = 'dot' + (i === currentIndex ? ' active' : '');
                    dot.addEventListener('click', () => navigateToImage(i, containerId));
                    dotsContainer.appendChild(dot);
                }}
            }}

            // 导航到指定图片
            function navigateToImage(index, containerId) {{
                const container = document.getElementById(containerId);
                container.dataset.currentIndex = index;
                updateLightboxImage(containerId);
                updateDots(containerId);
            }}

            // 更新小圆点状态
            function updateDots(containerId) {{
                const container = document.getElementById(containerId);
                const dotsContainer = container.querySelector('.lightbox-dots');
                const dots = dotsContainer.querySelectorAll('.dot');
                const currentIndex = parseInt(container.dataset.currentIndex || 0);

                dots.forEach((dot, index) => {{
                    dot.classList.toggle('active', index === currentIndex);
                }});
            }}

            // 切换图片
            function changeImage(lightboxId, direction) {{
                // 使用平滑切换（带滑动动画）
                smoothChangeImage(lightboxId, direction);
            }}

            // 键盘支持（全局事件，但通过DOM元素获取状态）
            document.addEventListener('keydown', function(e) {{
                const activeLightbox = document.querySelector('.lightbox[style=""display: flex;""]');
                if (!activeLightbox) return;

                const container = activeLightbox.closest('.image-grid-container');

                if (e.key === 'Escape') closeLightbox(activeLightbox.id);
                if (e.key === 'ArrowLeft') changeImage(activeLightbox.id, -1);
                if (e.key === 'ArrowRight') changeImage(activeLightbox.id, 1);
            }});

            // 简化展开/收起功能
            function toggleImageView(button, containerId, groupCount) {{
                event.preventDefault();
                const container = document.getElementById(containerId);
                const groups = container.querySelectorAll('.image-group');
                const isExpanded = groups[1] && !groups[1].classList.contains('hidden');

                for (let i = 1; i < groups.length; i++) {{
                    groups[i].classList.toggle('hidden', isExpanded);
                }}

                button.innerHTML = isExpanded 
                    ? `>> 展开更多 (${{groupCount - 1}} 组)` 
                    : `<< 收起 (${{groupCount - 1}} 组)`;
            }}
        </script>
    ");

        html.Append("</div>");
        return html.ToString();
    }



    protected void GenerateSeeMould(string strSql)
    {
        placeHolder1.Controls.Clear();

        using (SqlConnection conn = new SqlConnection(ConnStr))
        {
            conn.Open();
            SqlDataAdapter da = new SqlDataAdapter(strSql, conn);
            DataSet ds = new DataSet();
            da.Fill(ds);

            if (ds.Tables.Count == 0 || ds.Tables[0].Rows.Count == 0)
            {
                placeHolder1.Controls.Add(new Literal { Text = "<div class='container'><p>未找到客户数据</p></div>" });
                return;
            }

            DataTable dt = ds.Tables[0];
            DataRow dr = dt.Rows[0];

            EnumField[][] efTs = EnumField();
            Table[] tables = Tables();

            // 开始构建HTML
            StringBuilder htmlBuilder = new StringBuilder();

            // 添加HTML结构
            htmlBuilder.AppendLine("<div class=\"container\">");

            // 添加选项卡
            htmlBuilder.AppendLine("<div class=\"tabs\">");
            if (isMobile()) {
                htmlBuilder.AppendLine("  <div class=\"tab active\" onclick=\"showTab('info')\">客户信息</div>");
                htmlBuilder.AppendLine("  <div class=\"tab\" onclick=\"showTab('records')\">跟踪记录</div>");
            }
            htmlBuilder.AppendLine("</div>");

            // 客户信息部分
            htmlBuilder.AppendLine("<div id=\"info\" class=\"section\">");
            //if (isMobile()) {
            //    htmlBuilder.AppendLine("  <h1>客户信息</h1>");
            //}
            int colIndex = -1;
            foreach (Table table in tables)
            {
                foreach (Field field in table.fields)
                {
                    colIndex++;
                    if (colIndex >= dt.Columns.Count) continue;

                    string fieldValue = dr[colIndex]?.ToString() ?? "";
                    string displayValue = ProcessFieldValue(field, fieldValue, efTs);

                    if (!string.IsNullOrEmpty(displayValue))
                    {
                        htmlBuilder.AppendLine($"  <div class=\"value\">");
                        if (isMobile() && colIndex == 0)
                        {
                            htmlBuilder.AppendLine($"<h1>{HttpUtility.HtmlEncode(displayValue)}<h1>");
                        } else if (isMobile() && colIndex != 0) {
                            htmlBuilder.AppendLine($"  <span class=\"label\" >{field.fieldShowName}：</span>");
                            htmlBuilder.AppendLine($"  {HttpUtility.HtmlEncode(displayValue)}");
                        }
                        else
                        {
                            htmlBuilder.AppendLine($"  <span class=\"label\" style=\"color:#871F78;\">{field.fieldShowName}：</span>");
                            htmlBuilder.AppendLine($"  {HttpUtility.HtmlEncode(displayValue)}");
                        }
                        htmlBuilder.AppendLine("  </div>");
                    }
                }
            }
            htmlBuilder.AppendLine("</div>"); // 关闭客户信息部分

            // 跟踪记录部分
            htmlBuilder.AppendLine("<div id=\"records\" class=\"section hidden\">");
            if (isMobile())
            {
                // 先获取记录总数
                string countSql = "SELECT COUNT(*) FROM FollowRecord WHERE follow_client_id = @id";
                SqlCommand countComm = new SqlCommand(countSql, conn);
                countComm.Parameters.AddWithValue("@id", int.Parse(Request.QueryString["TaskID"].ToString()));
                LastRecordCount = (int)countComm.ExecuteScalar();
                htmlBuilder.AppendLine($"  <h1>共"+LastRecordCount+"条记录</h1>");
            }
            else {
                htmlBuilder.AppendLine("  <h4>跟踪记录</h4>");
            }
            // 获取跟踪记录数据
            int taskId;
            if (int.TryParse(Request.QueryString["TaskID"], out taskId))
            {
                string recordsHtml = GetRecord(taskId);
                htmlBuilder.AppendLine(recordsHtml);
            }
            else
            {
                htmlBuilder.AppendLine("<p>无效的任务ID</p>");
            }

            htmlBuilder.AppendLine("</div>"); // 关闭跟踪记录部分

            // 添加JavaScript
            htmlBuilder.AppendLine("<script>");
            htmlBuilder.AppendLine("function showTab(id) {");
            htmlBuilder.AppendLine("  var tabs = document.querySelectorAll('.tab');");
            htmlBuilder.AppendLine("  for (var i = 0; i < tabs.length; i++) {");
            htmlBuilder.AppendLine("    tabs[i].classList.remove('active');");
            htmlBuilder.AppendLine("  }");
            htmlBuilder.AppendLine("  ");
            htmlBuilder.AppendLine("  var sections = document.querySelectorAll('.section');");
            htmlBuilder.AppendLine("  for (var i = 0; i < sections.length; i++) {");
            htmlBuilder.AppendLine("    sections[i].classList.add('hidden');");
            htmlBuilder.AppendLine("  }");
            htmlBuilder.AppendLine("  ");
            htmlBuilder.AppendLine("  document.getElementById(id).classList.remove('hidden');");
            htmlBuilder.AppendLine("  ");
            htmlBuilder.AppendLine("  if (id === 'info') {");
            htmlBuilder.AppendLine("    document.querySelector('.tab:nth-child(1)').classList.add('active');");
            htmlBuilder.AppendLine("  } else {");
            htmlBuilder.AppendLine("    document.querySelector('.tab:nth-child(2)').classList.add('active');");
            htmlBuilder.AppendLine("  }");
            htmlBuilder.AppendLine("}");
            htmlBuilder.AppendLine("</script>");

            htmlBuilder.AppendLine("</div>"); // 关闭container

            // 输出到页面
            Literal htmlContent = new Literal();
            htmlContent.Text = htmlBuilder.ToString();
            placeHolder1.Controls.Add(htmlContent);
        }
    }

    public string GetRecord(int id)
    {

        using (SqlConnection conn = new SqlConnection(ConnStr))
        {
            conn.Open();



            string recordHtml = "";
            string strSql = "SELECT id,description, follow_time, follow_executor FROM FollowRecord " +
                            "WHERE follow_client_id = @id ORDER BY follow_time DESC";

            SqlCommand comm = new SqlCommand(strSql, conn);
            comm.Parameters.AddWithValue("@id", id);

            using (SqlDataReader dr = comm.ExecuteReader())
            {
                // 统一使用卡片式布局，不再区分移动端和桌面端
                if (isMobile())
                {
                
                while (dr.Read())
                {
                    int recordId = Convert.ToInt32(dr["id"]); // 追踪记录主键（关联图片表的 record_id）
                    string nickname = UserID_to_Nickname(Convert.ToInt32(dr["follow_executor"]));
                    string time = Convert.ToDateTime(dr["follow_time"]).ToString("yyyy/MM/dd HH:mm:ss");
                    string desc = dr["description"].ToString();

                    recordHtml += "<div class='record'>";
                    recordHtml += $"<h3>{time} - {nickname}</h3>";
                    recordHtml  += $"<p>{HttpUtility.HtmlEncode(desc)}</p>";
                    //生成追踪记录卡片
                    //recordHtml  += "<div class='record-card mobile-card'>";
                    //recordHtml  += $"<h3>{time} - {nickname}</h3>";
                    //recordHtml  += $"<p>{HttpUtility.HtmlEncode(desc)}</p>";
                    // 3. 查询并渲染关联图片（每行3张）
                    List<FollowRecordImage> images = GetImagesByRecordId(recordId);

                    recordHtml += GenerateImageGridHtml(images);

                    recordHtml += "</div>";

                }
                }
                else
				{
					// 桌面端：表格
					recordHtml += "<table border='1' style='text-align:center; width:100%; border-collapse: collapse;'>";
					recordHtml += "<tr style='background-color: #f0f0f0;'>";
					recordHtml += "<th style='width:450px; padding: 8px;'>内容</th>";
					recordHtml += "<th style='padding: 8px;'>跟踪时间</th>";
					recordHtml += "<th style='padding: 8px;'>跟踪者</th>";
					recordHtml += "<th style='padding: 8px;'>关联图片</th>";//新增
                    recordHtml += "</tr>";

					while (dr.Read())
					{
                        int recordId = Convert.ToInt32(dr["id"]);
                        recordHtml += "<tr>";
						recordHtml += "<td style='width:450px; padding: 8px; text-align:left;'>" + dr["description"].ToString() + "</td>";
						recordHtml += "<td style='padding: 8px;'>" + dr["follow_time"].ToString() + "</td>";
						recordHtml += "<td style='padding: 8px;'>" + UserID_to_Nickname(int.Parse(dr["follow_executor"].ToString())) + "</td>";
                        // 生成图片布局（嵌入表格列）
                        List<FollowRecordImage> images = GetImagesByRecordId(recordId);
                        recordHtml += $@"<td style='padding: 8px;'>{GenerateImageGridHtml(images)}</td>";
                        recordHtml += "</tr>";
					}
                    recordHtml += "</table>";
				}
			}

			return recordHtml;
        }
    }

    // 辅助方法：处理字段值的特殊逻辑
    private string ProcessFieldValue(Field field, string fieldValue, EnumField[][] efTs)
    {
        if (string.IsNullOrEmpty(fieldValue)) return string.Empty;

        switch (field.fieldType)
        {
            case EnumFieldType.numberType:
                if (field.fieldName == "PlanSpendTime")
                {
                    int spendTime;
                    if (int.TryParse(fieldValue, out spendTime))
                    {
                        int spendHour = spendTime / 60;
                        int spendMinute = spendTime % 60;

                        StringBuilder timeBuilder = new StringBuilder();
                        if (spendHour != 0) timeBuilder.Append(spendHour + "小时");
                        if (spendMinute != 0) timeBuilder.Append(spendMinute + "分钟");

                        return timeBuilder.ToString();
                    }
                }
                return fieldValue;

            case EnumFieldType.boolType:
                return (fieldValue == "1") ? "是" : "否";

            case EnumFieldType.enumType:
                int enumItem;
                if (int.TryParse(fieldValue, out enumItem))
                {
                    return (field.fieldName == "area") ?
                        (new Common()).GetTown(enumItem) :
                        efTs[field.enumTag][enumItem].itemDetail;
                }
                return fieldValue;

            default:
                return fieldValue;
        }
    }
    /// <summary>
    /// 根据追踪记录的 ID（record_id），查询所有关联图片
    /// </summary>
    /// <param name="recordId">追踪记录的主键 ID</param>
    /// <returns>关联图片列表</returns>
    private List<FollowRecordImage> GetImagesByRecordId(int recordId)
    {
        var images = new List<FollowRecordImage>();
        string queryStr = @"
        SELECT id, record_id, image_data, file_name, content_type, created_at 
        FROM FollowRecordImages 
        WHERE record_id = @recordId
    ";

        using (SqlConnection conn = new SqlConnection(ConnStr)) // ConnStr 是数据库连接字符串
        {
            conn.Open();
            SqlCommand cmd = new SqlCommand(queryStr, conn);
            cmd.Parameters.AddWithValue("@recordId", recordId); // 参数化查询防 SQL 注入

            using (SqlDataReader dr = cmd.ExecuteReader())
            {
                while (dr.Read())
                {
                    images.Add(new FollowRecordImage
                    {
                        Id = Convert.ToInt32(dr["id"]),
                        RecordId = Convert.ToInt32(dr["record_id"]),
                        ImageData = (byte[])dr["image_data"],
                        FileName = dr["file_name"].ToString(),
                        ContentType = dr["content_type"].ToString(),
                        CreatedAt = Convert.ToDateTime(dr["created_at"])
                    });
                }
            }
        }
        return images;
    }
    ///// <summary>
    ///// 生成查看模板
    ///// </summary>
    //protected void GenerateSeeMould(string strSql)
    //{
    //    int flag = 0;
    //    placeHolder1.Controls.Clear();

    //    SqlConnection conn = new SqlConnection(ConnStr);
    //    conn.Open();
    //    SqlDataAdapter da = new SqlDataAdapter(strSql, conn);
    //    DataSet ds = new DataSet();
    //    da.Fill(ds);
    //    DataTable dt = ds.Tables[0];
    //    DataRow dr = dt.Rows[0];

    //    EnumField[][] efTs = EnumField();
    //    Table[] tables = Tables();

    //    #region forTable
    //    int colIndex = -1;
    //    for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
    //    {
    //        #region forField
    //        for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
    //        {
    //            colIndex++;
    //            Literal ltlBrs = new Literal();
    //            ltlBrs.Text = "<br/>";
    //            placeHolder1.Controls.Add(ltlBrs);


    //            Label lbl = new Label();
    //            lbl.Text = tables[tableIndex].fields[fieldIndex].fieldShowName + "：";
    //            lbl.ForeColor = System.Drawing.ColorTranslator.FromHtml(InputParams.lblFieldShowNameColor);
    //            placeHolder1.Controls.Add(lbl);

    //            #region switch
    //            string strFldName = tables[tableIndex].fields[fieldIndex].fieldName;

    //            switch (tables[tableIndex].fields[fieldIndex].fieldType)
    //            {

    //                case EnumFieldType.numberType:
    //                    if (tables[tableIndex].fields[fieldIndex].fieldName == "PlanSpendTime")
    //                    {
    //                        int spendTime = int.Parse(dr[colIndex].ToString());
    //                        //以分钟为单位
    //                        //int spendYear = spendTime / (24 * 60 * 365);
    //                        //int spendMonth = (spendTime % (24 * 60 * 365)) / (24 * 60 * 30);
    //                        //int spendDay = (((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) / (24 * 60));
    //                        //int spendHour = (((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) % (24 * 60)) / 60;
    //                        //int spendMinute = ((((spendTime % (24 * 60 * 365)) % (24 * 60 * 30)) % (24 * 60))) % 60;

    //                        int spendHour = spendTime / 60;
    //                        int spendMinute = spendTime % 60;

    //                        string strSpendTime = "";
    //                        //if (spendYear != 0)
    //                        //{
    //                        //    strSpendTime += spendYear + "年";
    //                        //}
    //                        //if (spendMonth != 0)
    //                        //{
    //                        //    strSpendTime += spendMonth + "个月";

    //                        //}
    //                        //if (spendDay != 0)
    //                        //{
    //                        //    strSpendTime += spendDay + "天";

    //                        //}
    //                        if (spendHour != 0)
    //                        {
    //                            strSpendTime += spendHour + "小时";

    //                        }
    //                        if (spendMinute != 0)
    //                        {
    //                            strSpendTime += spendMinute + "分钟";
    //                        }

    //                        Literal ltlN = new Literal();
    //                        ltlN.Text = strSpendTime;
    //                        placeHolder1.Controls.Add(ltlN);

    //                    }
    //                    else
    //                    {
    //                        Literal ltlN = new Literal();
    //                        ltlN.Text = dr[colIndex].ToString();
    //                        placeHolder1.Controls.Add(ltlN);
    //                    }
    //                    continue;

    //                case EnumFieldType.boolType:

    //                    string text = "";
    //                    if (flag != 2)
    //                    {
    //                        if (int.Parse(dr[colIndex].ToString()) == 1)
    //                            text = "是";
    //                        else
    //                            text = "否";
    //                        flag++;

    //                    }
    //                    else
    //                    {
    //                        if (int.Parse(dr[colIndex].ToString()) == 1)
    //                            text = "男";
    //                        else
    //                            text = "女";
    //                        flag = 1;
    //                    }

    //                    Literal ltlB = new Literal();
    //                    ltlB.Text = text;
    //                    placeHolder1.Controls.Add(ltlB);
    //                    continue;

    //                //case EnumFieldType.picType:

    //                //    continue;

    //                case EnumFieldType.enumType:
    //                    int enumTag = tables[tableIndex].fields[fieldIndex].enumTag;
    //                    int enumItem = int.Parse(dr[colIndex].ToString());
    //                    Literal ltlE = new Literal();

    //                    if (tables[tableIndex].fields[fieldIndex].fieldName == "area")
    //                        ltlE.Text = (new Common()).GetTown(enumItem);
    //                    else
    //                        ltlE.Text = efTs[enumTag][enumItem].itemDetail;
    //                    placeHolder1.Controls.Add(ltlE);
    //                    continue;


    //                case EnumFieldType.longcharType:

    //                    if (dr[colIndex].ToString() != "")
    //                    {
    //                        Literal ltlBr1 = new Literal();
    //                        ltlBr1.Text = "<br/>";
    //                        placeHolder1.Controls.Add(ltlBr1);


    //                        Label lblD1 = new Label();
    //                        lblD1.Text = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + dr[colIndex].ToString();
    //                        lblD1.ForeColor = System.Drawing.ColorTranslator.FromHtml("#0000FF");
    //                        //lblD1.BackColor = System.Drawing.ColorTranslator.FromHtml("#F6FFFF");
    //                        //lblD1.Height = 50;
    //                        lblD1.Width = 700;
    //                        placeHolder1.Controls.Add(lblD1);

    //                        ////生成换行符
    //                        //Literal ltlBr2 = new Literal();
    //                        //ltlBr2.Text = "<br/>";
    //                        //placeHolder1.Controls.Add(ltlBr2);
    //                    }
    //                    continue;

    //                //case EnumFieldType.dateType:
    //                //    continue;
    //                //case EnumFieldType.charType:
    //                //    continue;

    //                default:
    //                    if ((dr[colIndex] != null) && (dr[colIndex].ToString() != ""))
    //                    {
    //                        Literal ltlT = new Literal();
    //                        ltlT.Text = dr[colIndex].ToString();
    //                        placeHolder1.Controls.Add(ltlT);
    //                    }
    //                    continue;

    //            }

    //            #endregion switch
    //        }
    //        #endregion forField
    //    }
    //    #endregion  forTable

    //    //添加追加记录

    //    Literal ltlRecord = new Literal();

    //    ltlRecord.Text = "<br> <p style='color:#871F78;'>追加记录：</p><br>" + (new Common()).GetRecord(int.Parse(Request.QueryString["TaskID"].ToString()));

    //    placeHolder1.Controls.Add(ltlRecord);

    //}

    /// <summary>
    /// 获得Sql字符串
    /// </summary>
    /// <returns></returns>
    protected string GetSqlStr(int TaskID)
    {
        EnumField[][] efTs = EnumField();
        Table[] tables = Tables();

        string strSelect = "SELECT  ";
        string strFrom = " FROM ";
        string strWhere = " WHERE " + pKey + "=" + TaskID;

        #region forTable
        for (int tableIndex = 0; tableIndex < tables.Length; tableIndex++)
        {
            string strTblName = tables[tableIndex].tableName;

            if (tableIndex == 0)
            {
                strFrom += strTblName;
            }
            else
            {
                strFrom += "," + strTblName;
            }
            if (tables[tableIndex].join.joinField != null)
            {
                strWhere += " AND " + strTblName + "." + tables[tableIndex].join.joinField + "=" + tables[tableIndex].join.joinRTable + "." + tables[tableIndex].join.joinRField;
            }

			int iGroupManager = int.Parse(Session["isGroupManager"].ToString());
			//if (iGroupManager != 3)
			//{
			//		strWhere += " and executor_id=" + Session["user_id"].ToString();
			//} 

            #region forField
            for (int fieldIndex = 0; fieldIndex < tables[tableIndex].fields.Length; fieldIndex++)
            {
                if (!((tableIndex == 0) && (fieldIndex == 0)))
                {
                    strSelect += ",";
                }
                strSelect += tables[tableIndex].tableName + "." + tables[tableIndex].fields[fieldIndex].fieldName;
            }
            #endregion forField
        }
        #endregion forTable
        string strSql = strSelect + strFrom + strWhere; 
        return strSql;
    }
}
