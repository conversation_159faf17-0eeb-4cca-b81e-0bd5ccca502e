﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.35026.282
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "http://localhost:8082", "http://localhost:8082", "{DEE1264B-8720-489D-9D11-79CFE977D3A2}"
	ProjectSection(WebsiteProperties) = preProject
		UseIISExpress = "false"
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.7.2"
		Debug.AspNetCompiler.VirtualPath = "/localhost_8081"
		Debug.AspNetCompiler.PhysicalPath = "..\ClientManage\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_8081\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_8081"
		Release.AspNetCompiler.PhysicalPath = "..\ClientManage\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_8081\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		SlnRelativePath = "..\ClientManage\"
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DEE1264B-8720-489D-9D11-79CFE977D3A2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DEE1264B-8720-489D-9D11-79CFE977D3A2}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F3285434-A389-41EC-9CF1-500B13637C66}
	EndGlobalSection
EndGlobal
